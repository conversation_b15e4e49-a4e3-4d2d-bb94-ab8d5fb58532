/**
 * 应用配置工具类
 * 用于获取外部配置文件中的配置项
 */

/**
 * 获取UI项目访问地址
 * @returns {string} UI项目的访问地址
 */
export function getUIProjectUrl () {
  // 检查全局配置是否存在
  if (!window.APP_CONFIG) {
    throw new Error('配置文件未加载！请检查 /static/config/app-config.js 文件是否存在且正确加载。');
  }

  // 检查UIProjectUrl配置是否存在
  if (!window.APP_CONFIG.UIProjectUrl) {
    throw new Error('UIProjectUrl 配置项未找到！请检查 app-config.js 文件中的 UIProjectUrl 配置。');
  }

  return window.APP_CONFIG.UIProjectUrl;
}

/**
 * 获取API基础地址
 * @returns {string} API基础地址
 */
export function getApiBaseUrl () {
  return getAppConfig('apiBaseUrl', 'https://localhost:7048/api');
}

/**
 * 获取上传配置
 * @returns {Object} 上传配置对象
 */
export function getUploadConfig () {
  return getAppConfig('upload', {
    maxFileSize: 2 * 1024 * 1024 * 1024,
    allowedVideoFormats: ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'],
    allowedImageFormats: ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
  });
}

/**
 * 获取压缩配置
 * @returns {Object} 压缩配置对象
 */
export function getCompressionConfig () {
  return getAppConfig('compression', {
    defaultQuality: 7,
    qualityRange: [1, 10],
    defaultEnabled: true
  });
}

/**
 * 获取功能开关配置
 * @returns {Object} 功能开关配置对象
 */
export function getFeaturesConfig () {
  return getAppConfig('features', {
    enableCache: true,
    enableOffline: false,
    enableAnalytics: false
  });
}

/**
 * 获取UI配置
 * @returns {Object} UI配置对象
 */
export function getUIConfig () {
  return getAppConfig('ui', {
    colors: {
      primary: '#186BFF',
      success: '#52C41A',
      warning: '#FAAD14',
      error: '#F5222D',
      info: '#186BFF'
    },
    pageSize: 20,
    maxUploadSize: 2 * 1024 * 1024 * 1024,
    animationDuration: 300
  });
}

/**
 * 获取应用配置项
 * @param {string} key 配置项键名，支持点号分隔的嵌套键名
 * @param {any} defaultValue 默认值
 * @returns {any} 配置项值
 */
export function getAppConfig (key, defaultValue = null) {
  if (!window.APP_CONFIG) {
    return defaultValue;
  }

  // 支持嵌套键名，如 'storage.expireTime'
  const keys = key.split('.');
  let value = window.APP_CONFIG;

  for (const k of keys) {
    if (value && typeof value === 'object' && value[k] !== undefined) {
      value = value[k];
    } else {
      return defaultValue;
    }
  }

  return value;
}

/**
 * 设置应用配置项
 * @param {string} key 配置项键名
 * @param {any} value 配置项值
 */
export function setAppConfig (key, value) {
  if (window.setAppConfig) {
    window.setAppConfig(key, value);
  }
}

/**
 * 检查配置是否已加载
 * @returns {boolean} 配置是否已加载
 */
export function isConfigLoaded () {
  return !!(window.APP_CONFIG);
}

/**
 * 等待配置加载完成
 * @param {number} timeout 超时时间（毫秒）
 * @returns {Promise<boolean>} 是否加载成功
 */
export function waitForConfig (timeout = 5000) {
  return new Promise((resolve) => {
    if (isConfigLoaded()) {
      resolve(true);
      return;
    }

    let attempts = 0;
    const maxAttempts = timeout / 100;

    const checkConfig = () => {
      attempts++;
      if (isConfigLoaded()) {
        resolve(true);
      } else if (attempts >= maxAttempts) {
        resolve(false);
      } else {
        setTimeout(checkConfig, 100);
      }
    };

    checkConfig();
  });
}
