import{_ as a,H as s,o as t,c as e,w as l,g as o,h as c,t as d,a8 as i,i as n,k as _}from"./index-qwAcQRkd.js";const r=a({name:"PageHeader",props:{title:{type:String,default:""},showBack:{type:Boolean,default:!0}},methods:{goBack(){this.showBack&&s()}}},[["render",function(a,s,r,u,f,g){const h=n,p=_;return t(),e(h,null,{default:l((()=>[o(h,{class:"_page-header"},{default:l((()=>[o(h,{class:"_back-btn",onClick:g.goBack},{default:l((()=>[o(h,{class:"_back-chevron"},{default:l((()=>[c("<")])),_:1})])),_:1},8,["onClick"]),o(h,{class:"_title-container"},{default:l((()=>[o(p,{class:"_page-title"},{default:l((()=>[c(d(r.title),1)])),_:1}),i(a.$slots,"subtitle",{},void 0,!0)])),_:3}),o(h,{class:"_right-slot"},{default:l((()=>[i(a.$slots,"right",{},void 0,!0)])),_:3})])),_:3}),o(h,{class:"_page-header-2"})])),_:3})}],["__scopeId","data-v-36da0fb3"]]);export{r as P};
