import{_ as t,o as a,c as e,w as s,h as l,t as n,n as o,k as u,l as i,s as r,m as c,p as d,q as f,u as m,i as w,g as _,j as h}from"./index-qwAcQRkd.js";import{T as p}from"./TimeFilter.LvSGfQE0.js";import{r as v,a as C}from"./adminAuthService.2Y4HA-uG.js";import{g}from"./dashboard.ZhYzQ-QX.js";function V(t={}){return v.get("/Statistics/overview",t)}const b=t({components:{CountUp:t({name:"CountUp",props:{endValue:{type:[Number,String],required:!0,default:0},suffix:{type:String,default:""},duration:{type:Number,default:1e3},decimals:{type:Number,default:0},customClass:{type:String,default:""}},data:()=>({displayValue:"0",startValue:0,interval:null}),watch:{endValue:{handler(t){null!=t&&this.startAnimation(t)},immediate:!0}},methods:{startAnimation(t){this.interval&&clearInterval(this.interval);const a=this.parseValue(t),e=this.parseValue(this.displayValue);if(e===a)return void(this.displayValue=this.formatNumber(a));const s=this.duration,l=Math.min(s/20,25),n=(a-e)/20;let o=0;this.interval=setInterval((()=>{if(o++,o>=20)this.displayValue=this.formatNumber(a),clearInterval(this.interval);else{const t=e+n*o;this.displayValue=this.formatNumber(t)}}),l)},parseValue(t){const a=parseFloat(t);return isNaN(a)?0:a},formatNumber(t){const a=Number(t).toFixed(this.decimals);return isNaN(a)?"0":a}},beforeDestroy(){this.interval&&clearInterval(this.interval)}},[["render",function(t,i,r,c,d,f){const m=u;return a(),e(m,{class:o(r.customClass)},{default:s((()=>[l(n(d.displayValue)+n(r.suffix),1)])),_:1},8,["class"])}],["__scopeId","data-v-068eabf7"]]),TimeFilter:p},data:()=>({statisticsData:{totalUsers:1234,newUsersToday:56,viewerCount:789,completeRate:0,answerUserCount:0,correctRate:0,totalRewardAmount:0,untaggedUserCount:0,videoStats:{viewCount:2345,completeViewCount:1876,completeRate:79.98,avgViewDuration:0},quizStats:{answerCount:1567,correctAnswerCount:1234,correctRate:78.75},rewardStats:{rewardCount:45,rewardAmountYuan:1250.5}},activeTimeFilter:"today",customDateRange:null,sectionState:{courses:!0,quizzes:!0,rewards:!0}}),onLoad(){const t=window.location.href;if(console.log("首页onLoad - 当前URL:",t),t.includes("/pages/video/index")){console.log("检测到视频页面访问，直接跳转");const t=window.location.hash;if(t&&t.length>1){const a=t.substring(1);return console.log("跳转到视频页面:",a),void i({url:a})}}if(console.log("进行后台用户认证检查"),!C.isLoggedIn()||!C.isSessionValid())return void C.redirectToLogin();const a=C.getUserType();console.log("当前后台用户类型:",a),this.initDashboardData(),r()},onShow(){r()},methods:{async initDashboardData(){try{c({title:"加载数据中..."}),await this.loadStatisticsData("today"),d()}catch(t){console.error("初始化数据失败:",t),d(),f({title:"数据加载失败",icon:"none",duration:2e3})}},async loadStatisticsData(t,a=null,e=null){try{let l={};if(a&&e)l={startDate:a,endDate:e};else if("today"===t){const t=new Date,a=t=>`${t.getFullYear()}-${(t.getMonth()+1).toString().padStart(2,"0")}-${t.getDate().toString().padStart(2,"0")}`;l.startDate=a(t),l.endDate=a(t)}let n=null,o=null;try{[n,o]=await Promise.all([V(l),g(l)])}catch(s){console.error("API调用失败:",s)}const u=n&&n.success,i=o&&o.success;if(u&&i){const t=n.data,a=o.data;this.statisticsData={totalUsers:a.totalMembers||0,newUsersToday:a.todayNewMembers||0,viewerCount:a.viewerCount||0,completeRate:a.completeRate||0,answerUserCount:a.answerUserCount||0,correctRate:a.correctRate||0,totalRewardAmount:a.totalRewardAmount||0,untaggedUserCount:a.untaggedUserCount||0,videoStats:{viewCount:t.totalViewCount||0,completeViewCount:t.totalCompleteViewCount||0,completeRate:t.avgCompleteRate||0,avgViewDuration:0},quizStats:{answerCount:t.totalAnswerCount||0,correctAnswerCount:t.totalCorrectAnswerCount||0,correctRate:t.avgCorrectRate||0},rewardStats:{rewardCount:t.totalRewardCount||0,rewardAmountYuan:t.totalRewardAmountYuan||0}}}else console.warn("API数据获取失败，使用模拟数据:",{overviewSuccess:u,keyMetricsSuccess:i,overviewResponse:n?n.msg:"null",keyMetricsResponse:o?o.msg:"null"}),f({title:"数据加载失败",icon:"none",duration:2e3})}catch(l){throw console.error("加载统计数据失败:",l),l}},async selectTimeTab(t){try{c({title:"加载中..."}),await this.loadStatisticsData(t),d()}catch(a){console.error("切换时间标签失败:",a),d(),f({title:"数据加载失败",icon:"none",duration:2e3})}},async handleTimeFilterChange(t){try{this.activeTimeFilter=t,this.customDateRange={startDate:t.startDate,endDate:t.endDate},console.log("时间筛选变化:",t),c({title:"加载中..."}),await this.loadStatisticsData(t.type,t.startDate,t.endDate),d(),f({title:`已加载${t.startDate}至${t.endDate}的数据`,icon:"none",duration:2e3})}catch(a){console.error("时间筛选数据加载失败:",a),d(),f({title:"数据加载失败",icon:"none",duration:2e3})}}}},[["render",function(t,n,o,i,r,c){const d=m("CountUp"),f=w,p=u,v=m("TimeFilter");return a(),e(f,{class:"container"},{default:s((()=>[_(f,{class:"page-header"},{default:s((()=>[_(f,{class:"summary-section"},{default:s((()=>[_(f,{class:"stats-grid"},{default:s((()=>[_(f,{class:"stat-card"},{default:s((()=>[_(f,{class:"stat-value"},{default:s((()=>[_(d,{endValue:r.statisticsData.totalUsers,suffix:"人",duration:1e3,customClass:"stat-number"},null,8,["endValue"])])),_:1}),_(p,{class:"stat-label"},{default:s((()=>[l("会员总数")])),_:1})])),_:1}),_(f,{class:"stat-card"},{default:s((()=>[_(f,{class:"stat-value"},{default:s((()=>[_(d,{endValue:r.statisticsData.newUsersToday,suffix:"人",duration:1e3,customClass:"stat-number"},null,8,["endValue"])])),_:1}),_(p,{class:"stat-label"},{default:s((()=>[l("今日新增")])),_:1})])),_:1}),_(f,{class:"stat-card"},{default:s((()=>[_(f,{class:"stat-value"},{default:s((()=>[_(d,{endValue:r.statisticsData.viewerCount,suffix:"人",duration:1e3,customClass:"stat-number"},null,8,["endValue"])])),_:1}),_(p,{class:"stat-label"},{default:s((()=>[l("今日观看")])),_:1})])),_:1})])),_:1})])),_:1}),_(f,{class:"time-filter-section"},{default:s((()=>[_(f,{class:"time-filter-title"},{default:s((()=>[_(p,null,{default:s((()=>[l("时间筛选")])),_:1}),_(p,{class:"time-filter-subtitle"},{default:s((()=>[l("选择查看不同时间段的数据")])),_:1})])),_:1}),_(v,{modelValue:r.activeTimeFilter,"onUpdate:modelValue":n[0]||(n[0]=t=>r.activeTimeFilter=t),onChange:c.handleTimeFilterChange},null,8,["modelValue","onChange"])])),_:1})])),_:1}),_(f,{class:"page-content"},{default:s((()=>[_(f,{class:"data-section"},{default:s((()=>[_(f,{class:"section-header"},{default:s((()=>[_(f,{class:"section-title-wrapper"},{default:s((()=>[_(p,{class:"section-title"},{default:s((()=>[l("课程统计")])),_:1})])),_:1})])),_:1}),r.sectionState.courses?(a(),e(f,{key:0,class:"section-content"},{default:s((()=>[_(f,{class:"stats-grid"},{default:s((()=>[_(f,{class:"stat-card"},{default:s((()=>[_(p,{class:"stat-label"},{default:s((()=>[l("观看次数")])),_:1}),_(f,{class:"stat-value"},{default:s((()=>[_(d,{endValue:r.statisticsData.videoStats.viewCount,suffix:"次",duration:1e3,customClass:"stat-number"},null,8,["endValue"])])),_:1})])),_:1}),_(f,{class:"stat-card"},{default:s((()=>[_(p,{class:"stat-label"},{default:s((()=>[l("完播次数")])),_:1}),_(f,{class:"stat-value"},{default:s((()=>[_(d,{endValue:r.statisticsData.videoStats.completeViewCount,suffix:"次",duration:1e3,customClass:"stat-number"},null,8,["endValue"])])),_:1})])),_:1}),_(f,{class:"stat-card"},{default:s((()=>[_(p,{class:"stat-label"},{default:s((()=>[l("完播率")])),_:1}),_(f,{class:"stat-value"},{default:s((()=>[_(d,{endValue:r.statisticsData.videoStats.completeRate,suffix:"%",duration:1e3,decimals:2,customClass:"stat-number"},null,8,["endValue"])])),_:1})])),_:1})])),_:1})])),_:1})):h("",!0)])),_:1}),_(f,{class:"data-section"},{default:s((()=>[_(f,{class:"section-header"},{default:s((()=>[_(f,{class:"section-title-wrapper"},{default:s((()=>[_(p,{class:"section-title"},{default:s((()=>[l("答题统计")])),_:1})])),_:1})])),_:1}),r.sectionState.quizzes?(a(),e(f,{key:0,class:"section-content"},{default:s((()=>[_(f,{class:"stats-grid"},{default:s((()=>[_(f,{class:"stat-card"},{default:s((()=>[_(p,{class:"stat-label"},{default:s((()=>[l("答题次数")])),_:1}),_(f,{class:"stat-value"},{default:s((()=>[_(d,{endValue:r.statisticsData.quizStats.answerCount,suffix:"次",duration:1e3,customClass:"stat-number"},null,8,["endValue"])])),_:1})])),_:1}),_(f,{class:"stat-card"},{default:s((()=>[_(p,{class:"stat-label"},{default:s((()=>[l("正确次数")])),_:1}),_(f,{class:"stat-value"},{default:s((()=>[_(d,{endValue:r.statisticsData.quizStats.correctAnswerCount,suffix:"次",duration:1e3,customClass:"stat-number"},null,8,["endValue"])])),_:1})])),_:1}),_(f,{class:"stat-card"},{default:s((()=>[_(p,{class:"stat-label"},{default:s((()=>[l("正确率")])),_:1}),_(f,{class:"stat-value"},{default:s((()=>[_(d,{endValue:r.statisticsData.quizStats.correctRate,suffix:"%",duration:1e3,decimals:2,customClass:"stat-number"},null,8,["endValue"])])),_:1})])),_:1})])),_:1})])),_:1})):h("",!0)])),_:1}),_(f,{class:"data-section"},{default:s((()=>[_(f,{class:"section-header"},{default:s((()=>[_(f,{class:"section-title-wrapper"},{default:s((()=>[_(p,{class:"section-title"},{default:s((()=>[l("红包统计")])),_:1})])),_:1})])),_:1}),r.sectionState.rewards?(a(),e(f,{key:0,class:"section-content"},{default:s((()=>[_(f,{class:"stats-grid grid-2"},{default:s((()=>[_(f,{class:"stat-card"},{default:s((()=>[_(p,{class:"stat-label"},{default:s((()=>[l("红包数量")])),_:1}),_(f,{class:"stat-value"},{default:s((()=>[_(d,{endValue:r.statisticsData.rewardStats.rewardCount,suffix:"个",duration:1e3,customClass:"stat-number"},null,8,["endValue"])])),_:1})])),_:1}),_(f,{class:"stat-card"},{default:s((()=>[_(p,{class:"stat-label"},{default:s((()=>[l("红包金额")])),_:1}),_(f,{class:"stat-value"},{default:s((()=>[_(d,{endValue:r.statisticsData.rewardStats.rewardAmountYuan,suffix:"元",duration:1e3,decimals:2,customClass:"stat-number"},null,8,["endValue"])])),_:1})])),_:1})])),_:1})])),_:1})):h("",!0)])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-5f42ba25"]]);export{b as default};
