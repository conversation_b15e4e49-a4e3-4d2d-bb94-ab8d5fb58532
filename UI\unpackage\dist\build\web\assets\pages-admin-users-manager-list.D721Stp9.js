import{_ as a,C as e,c as s,w as n,i as t,o as r,g as o,h as d,k as l}from"./index-qwAcQRkd.js";const u=a({name:"ManagerListRedirect",onLoad(){console.log("manager-list.vue 重定向到统一页面"),e({url:"/pages/admin/users/user-management?type=manager"})}},[["render",function(a,e,u,c,i,g){const m=l,f=t;return r(),s(f,{class:"redirect-page"},{default:n((()=>[o(f,{class:"loading"},{default:n((()=>[o(m,null,{default:n((()=>[d("正在跳转到管理员页面...")])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-01a5567f"]]);export{u as default};
