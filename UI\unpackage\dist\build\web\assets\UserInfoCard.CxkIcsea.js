import{_ as e,o as t,c as s,w as a,g as o,a7 as n,h as l,t as i,J as u,n as r,j as c,i as f,k as d,a8 as m,M as p,q as y,u as I}from"./index-qwAcQRkd.js";import{m as h}from"./media-common.BAxpCZbC.js";const w=e({name:"UserInfoCard",components:{UserBasicInfo:e({name:"UserBasicInfo",mixins:[h],props:{userInfo:{type:Object,required:!0},showActionBtn:{type:Boolean,default:!1},actionIcon:{type:String,default:"icon-more"}},computed:{avatarUrl(){return this.buildCompleteFileUrl(this.userInfo.avatar)||"/assets/images/default-avatar.png"}},methods:{getRoleText(){switch(this.userInfo.type){case"agent":return"代理";case"employee":return"员工";case"user":return"用户";default:return"未知"}},getRoleTagClass(){switch(this.userInfo.type){case"agent":return"agent-tag";case"employee":return"employee-tag";case"user":return"user-tag";default:return""}},formatDate:e=>e?e.includes(" ")?e.split(" ")[0]:e:"",viewEmployee(e){this.$emit("view-employee",e)},viewManager(e){this.$emit("view-manager",e)}}},[["render",function(e,m,p,y,I,h){const w=f,_=d;return t(),s(w,{class:"user-basic-info"},{default:a((()=>[o(w,{class:"user-avatar",style:n({backgroundImage:`url(${h.avatarUrl})`})},null,8,["style"]),o(w,{class:"user-info"},{default:a((()=>[o(w,{class:"user-top-row"},{default:a((()=>[o(w,{class:"user-name-container"},{default:a((()=>[o(_,{class:"user-name"},{default:a((()=>[l(i(p.userInfo.username||p.userInfo.nickname),1)])),_:1}),o(w,{class:"copy-id-btn",onClick:m[0]||(m[0]=u((t=>e.$emit("copy-id")),["stop"]))},{default:a((()=>[o(_,{class:"copy-id-icon"},{default:a((()=>[l("复制ID")])),_:1})])),_:1})])),_:1}),o(w,{class:"user-type"},{default:a((()=>[o(_,{class:r(["type-tag",h.getRoleTagClass()])},{default:a((()=>[l(i(h.getRoleText()),1)])),_:1},8,["class"])])),_:1})])),_:1}),o(w,{class:"user-phone"},{default:a((()=>[l(i(p.userInfo.phone),1)])),_:1}),o(w,{class:"user-meta-info"},{default:a((()=>[o(w,{class:"meta-item"},{default:a((()=>[o(_,{class:"meta-label"},{default:a((()=>[l("注册:")])),_:1}),o(_,{class:"meta-value"},{default:a((()=>[l(i(h.formatDate(p.userInfo.registerTime)),1)])),_:1})])),_:1}),"employee"===p.userInfo.type?(t(),s(w,{key:0,class:"meta-item"},{default:a((()=>[o(_,{class:"meta-label"},{default:a((()=>[l("推广:")])),_:1}),o(_,{class:"meta-value"},{default:a((()=>[l(i(p.userInfo.userCount||0)+"人",1)])),_:1})])),_:1})):c("",!0),"agent"===p.userInfo.type?(t(),s(w,{key:1,class:"meta-item"},{default:a((()=>[o(_,{class:"meta-label"},{default:a((()=>[l("管理:")])),_:1}),o(_,{class:"meta-value"},{default:a((()=>[l(i(p.userInfo.employeeCount||p.userInfo.userCount||0)+"人",1)])),_:1})])),_:1})):c("",!0),"user"===p.userInfo.type&&p.userInfo.employeeId?(t(),s(w,{key:2,class:"meta-item"},{default:a((()=>[o(_,{class:"meta-label"},{default:a((()=>[l("所属:")])),_:1}),o(_,{class:"meta-value link-text",onClick:m[1]||(m[1]=e=>h.viewEmployee(p.userInfo.employeeId))},{default:a((()=>[l(i(p.userInfo.employeeName||"员工"),1)])),_:1})])),_:1})):c("",!0),"employee"===p.userInfo.type&&p.userInfo.managerId?(t(),s(w,{key:3,class:"meta-item"},{default:a((()=>[o(_,{class:"meta-label"},{default:a((()=>[l("所属:")])),_:1}),o(_,{class:"meta-value link-text",onClick:m[2]||(m[2]=e=>h.viewManager(p.userInfo.managerId))},{default:a((()=>[l(i(p.userInfo.managerName||"管理"),1)])),_:1})])),_:1})):c("",!0)])),_:1})])),_:1}),p.showActionBtn?(t(),s(w,{key:0,class:"action-btn",onClick:m[3]||(m[3]=t=>e.$emit("action"))},{default:a((()=>[o(_,{class:r(["iconfont",p.actionIcon])},null,8,["class"])])),_:1})):c("",!0)])),_:1})}],["__scopeId","data-v-182d325e"]]),UserStatistics:e({name:"UserStatistics",props:{userInfo:{type:Object,required:!0},timeFilter:{type:String,default:"today"}},methods:{getViewCount(){return this.userInfo.watchedVideos||this.userInfo.totalViews&&this.userInfo.totalViews[this.timeFilter]||0},getQuizCount(){return this.userInfo.completedQuizzes||this.userInfo.totalQuizzes&&this.userInfo.totalQuizzes[this.timeFilter]||0},getRewardAmount(){const e=this.userInfo.totalRewards;return e?"object"==typeof e?e[this.timeFilter]||0:e:0},getRewardLabel(){return"user"!==this.userInfo.type?"奖励总额":"获得奖励"}}},[["render",function(e,n,u,r,c,m){const p=d,y=f;return t(),s(y,{class:"stats-section"},{default:a((()=>[o(y,{class:"stat-item"},{default:a((()=>[o(y,{class:"stat-content"},{default:a((()=>[o(p,{class:"stat-value"},{default:a((()=>[l("观看视频:"+i(m.getViewCount()),1)])),_:1})])),_:1})])),_:1}),o(y,{class:"stat-item"},{default:a((()=>[o(y,{class:"stat-content"},{default:a((()=>[o(p,{class:"stat-value"},{default:a((()=>[l("答题数量:"+i(m.getQuizCount()),1)])),_:1})])),_:1})])),_:1}),o(y,{class:"stat-item"},{default:a((()=>[o(y,{class:"stat-content"},{default:a((()=>[o(p,{class:"stat-value"},{default:a((()=>[l(i(m.getRewardLabel())+":"+i(m.getRewardAmount()),1)])),_:1})])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-679c7546"]]),UserActions:e({name:"UserActions",props:{userInfo:{type:Object,required:!0},showDetailBtn:{type:Boolean,default:!0},showUsersBtn:{type:Boolean,default:!0},showEmployeesBtn:{type:Boolean,default:!0},showAccountBtn:{type:Boolean,default:!1}}},[["render",function(e,n,r,p,y,I){const h=d,w=f;return t(),s(w,{class:"footer-btns"},{default:a((()=>[r.showDetailBtn?(t(),s(w,{key:0,class:"footer-btn detail-btn",onClick:n[0]||(n[0]=t=>e.$emit("view-detail"))},{default:a((()=>[o(h,{class:"iconfont icon-detail"}),o(h,null,{default:a((()=>[l("查看详情")])),_:1})])),_:1})):c("",!0),"employee"===r.userInfo.type&&r.showUsersBtn?(t(),s(w,{key:1,class:"footer-btn users-btn",onClick:n[1]||(n[1]=t=>e.$emit("view-users"))},{default:a((()=>[o(h,{class:"iconfont icon-users"}),o(h,null,{default:a((()=>[l("查看用户")])),_:1})])),_:1})):c("",!0),"agent"===r.userInfo.type&&r.showEmployeesBtn?(t(),s(w,{key:2,class:"footer-btn employees-btn",onClick:n[2]||(n[2]=t=>e.$emit("view-employees"))},{default:a((()=>[o(h,{class:"iconfont icon-users"}),o(h,null,{default:a((()=>[l("查看员工")])),_:1})])),_:1})):c("",!0),r.showAccountBtn?(t(),s(w,{key:3,class:"footer-btn account-btn",onClick:n[3]||(n[3]=u((t=>e.$emit("account-action")),["stop"]))},{default:a((()=>[o(h,{class:"iconfont icon-account"}),o(h,null,{default:a((()=>[l(i(r.userInfo.disabled?"启用账号":"禁用账号"),1)])),_:1})])),_:1})):c("",!0),m(e.$slots,"buttons",{},void 0,!0)])),_:3})}],["__scopeId","data-v-38aed260"]])},props:{userInfo:{type:Object,required:!0},showActionBtn:{type:Boolean,default:!1},actionIcon:{type:String,default:"icon-more"},showFooterBtns:{type:Boolean,default:!0},showDetailBtn:{type:Boolean,default:!0},showUsersBtn:{type:Boolean,default:!0},showEmployeesBtn:{type:Boolean,default:!0},showAccountBtn:{type:Boolean,default:!1},timeFilter:{type:String,default:"today"}},computed:{actualTimeFilter(){return this.timeFilter||"today"}},methods:{copyUserId(){p({data:this.userInfo.id.toString(),success:()=>{y({title:"ID已复制",icon:"success"})}})},handleAction(){this.$emit("action",this.userInfo)},viewDetail(){this.$emit("view-detail",this.userInfo)},viewUsers(){this.$emit("view-users",this.userInfo)},viewEmployees(){this.$emit("view-employees",this.userInfo)},handleAccountAction(){this.$emit("account-action",this.userInfo)}}},[["render",function(e,n,l,i,u,r){const d=I("UserBasicInfo"),p=I("UserStatistics"),y=f,h=I("UserActions");return t(),s(y,{class:"user-info-card"},{default:a((()=>[o(d,{userInfo:l.userInfo,showActionBtn:l.showActionBtn,actionIcon:l.actionIcon,onAction:r.handleAction,onCopyId:r.copyUserId},null,8,["userInfo","showActionBtn","actionIcon","onAction","onCopyId"]),o(p,{userInfo:l.userInfo,timeFilter:r.actualTimeFilter},null,8,["userInfo","timeFilter"]),e.$slots.extra?(t(),s(y,{key:0,class:"extra-section"},{default:a((()=>[m(e.$slots,"extra",{},void 0,!0)])),_:3})):c("",!0),l.showFooterBtns?(t(),s(h,{key:1,userInfo:l.userInfo,showDetailBtn:l.showDetailBtn,showUsersBtn:l.showUsersBtn,showEmployeesBtn:l.showEmployeesBtn,showAccountBtn:l.showAccountBtn,onViewDetail:r.viewDetail,onViewUsers:r.viewUsers,onViewEmployees:r.viewEmployees,onAccountAction:r.handleAccountAction},{default:a((()=>[m(e.$slots,"buttons",{},void 0,!0)])),_:3},8,["userInfo","showDetailBtn","showUsersBtn","showEmployeesBtn","showAccountBtn","onViewDetail","onViewUsers","onViewEmployees","onAccountAction"])):c("",!0)])),_:3})}],["__scopeId","data-v-6cd4df28"]]);export{w as U};
