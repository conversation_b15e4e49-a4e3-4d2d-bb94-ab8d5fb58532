/**
 * 权限混入 - 提供统一的权限检查方法
 * 在页面和组件中使用，提供便捷的权限验证功能
 */

import adminAuthService from '@/utils/adminAuthService.js'
import { PERMISSION_CONFIG } from '@/utils/config.js'

export default {
  data() {
    return {
      // 当前用户信息
      currentUser: null,
      currentUserType: null,
      currentPermissions: []
    }
  },

  computed: {
    /**
     * 是否为超管
     */
    isAdmin() {
      return this.currentUserType === 'admin' || this.currentUserType === 'super_admin'
    },

    /**
     * 是否为管理员（包括超管和管理）
     */
    isManager() {
      return ['admin', 'super_admin', 'manager', 'agent'].includes(this.currentUserType)
    },

    /**
     * 是否为员工（包括管理员以上）
     */
    isEmployee() {
      return ['admin', 'super_admin', 'manager', 'agent', 'employee'].includes(this.currentUserType)
    },

    /**
     * 是否为普通用户
     */
    isUser() {
      return this.currentUserType === 'user'
    },

    /**
     * 当前用户权限级别
     */
    currentPermissionLevel() {
      return PERMISSION_CONFIG.PERMISSION_LEVELS[this.currentUserType] || 0
    }
  },

  created() {
    this.initPermissionData()
  },

  methods: {
    /**
     * 初始化权限数据
     */
    initPermissionData() {
      this.currentUser = adminAuthService.getLoginInfo()
      this.currentUserType = adminAuthService.getUserType()
      this.currentPermissions = this.getCurrentPermissions()
    },

    /**
     * 获取当前用户权限列表
     */
    getCurrentPermissions() {
      if (!this.currentUserType) return []
      return PERMISSION_CONFIG.ROLE_PERMISSIONS[this.currentUserType] || []
    },

    /**
     * 检查是否有指定权限
     * @param {string} permission - 权限名称
     * @returns {boolean}
     */
    hasPermission(permission) {
      if (!this.currentUserType) return false
      
      // 超管拥有所有权限
      if (this.currentPermissions.includes('*')) return true
      
      return this.currentPermissions.includes(permission)
    },

    /**
     * 检查是否有指定角色
     * @param {string|Array} roles - 角色名称或角色数组
     * @returns {boolean}
     */
    hasRole(roles) {
      if (!this.currentUserType) return false
      
      if (Array.isArray(roles)) {
        return roles.includes(this.currentUserType)
      }
      
      return this.currentUserType === roles
    },

    /**
     * 检查是否有指定权限级别
     * @param {string} requiredRole - 需要的最低角色
     * @returns {boolean}
     */
    hasPermissionLevel(requiredRole) {
      if (!this.currentUserType) return false
      
      const currentLevel = PERMISSION_CONFIG.PERMISSION_LEVELS[this.currentUserType] || 0
      const requiredLevel = PERMISSION_CONFIG.PERMISSION_LEVELS[requiredRole] || 0
      
      return currentLevel >= requiredLevel
    },

    /**
     * 检查是否可以访问指定页面
     * @param {string} pagePath - 页面路径
     * @returns {boolean}
     */
    canAccessPage(pagePath) {
      if (!this.currentUserType) return false
      
      const allowedRoles = PERMISSION_CONFIG.PAGE_PERMISSIONS[pagePath]
      if (!allowedRoles) return true // 如果没有配置权限，默认允许访问
      
      return allowedRoles.includes(this.currentUserType)
    },

    /**
     * 检查是否可以使用指定功能
     * @param {string} feature - 功能名称
     * @returns {boolean}
     */
    canUseFeature(feature) {
      if (!this.currentUserType) return false
      
      const allowedRoles = PERMISSION_CONFIG.FEATURE_PERMISSIONS[feature]
      if (!allowedRoles) return false // 如果没有配置权限，默认不允许
      
      return allowedRoles.includes(this.currentUserType)
    },

    /**
     * 检查是否可以管理指定用户
     * @param {Object} targetUser - 目标用户信息
     * @returns {boolean}
     */
    canManageUser(targetUser) {
      if (!this.currentUserType || !targetUser) return false
      
      // 超管可以管理所有用户
      if (this.currentUserType === 'admin' || this.currentUserType === 'super_admin') {
        return targetUser.userType !== 'super_admin' || this.currentUserType === 'super_admin'
      }
      
      // 管理员可以管理员工和用户
      if (this.currentUserType === 'manager' || this.currentUserType === 'agent') {
        return ['employee', 'user'].includes(targetUser.userType)
      }
      
      // 员工只能管理用户
      if (this.currentUserType === 'employee') {
        return targetUser.userType === 'user'
      }
      
      return false
    },

    /**
     * 检查是否可以查看指定用户
     * @param {Object} targetUser - 目标用户信息
     * @returns {boolean}
     */
    canViewUser(targetUser) {
      if (!this.currentUserType || !targetUser) return false
      
      // 超管和管理员可以查看所有用户
      if (['admin', 'super_admin', 'manager', 'agent'].includes(this.currentUserType)) {
        return true
      }
      
      // 员工可以查看用户
      if (this.currentUserType === 'employee') {
        return ['user'].includes(targetUser.userType)
      }
      
      // 用户只能查看自己
      if (this.currentUserType === 'user') {
        return targetUser.id === this.currentUser?.userId
      }
      
      return false
    },

    /**
     * 获取用户显示名称
     * @param {string} userType - 用户类型
     * @returns {string}
     */
    getUserTypeName(userType) {
      const typeNames = {
        super_admin: '超级管理员',
        admin: '管理员',
        manager: '管理',
        agent: '管理',
        employee: '员工',
        user: '用户'
      }
      return typeNames[userType] || '未知'
    },

    /**
     * 刷新权限数据
     */
    refreshPermissionData() {
      this.initPermissionData()
    }
  }
}
