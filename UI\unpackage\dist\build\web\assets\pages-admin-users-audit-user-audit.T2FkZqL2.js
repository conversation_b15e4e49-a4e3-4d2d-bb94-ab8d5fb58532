import{_ as e,r as t,Y as a,a as s,P as i,Q as l,R as r,T as o,d as n,e as u,aj as d,Z as c,b as p,a4 as m,o as f,c as h,w as y,v as g,x,F as v,j as C,g as T,h as k,t as w,i as _,k as j,N as b,u as L,as as V}from"./index-qwAcQRkd.js";import{b as D}from"./format.t5pgP9mx.js";import{r as R}from"./adminAuthService.2Y4HA-uG.js";function $(e,t){return R.post(`/UserAudit/audit-user/${e}`,t)}const z=e({components:{AuditList:e({name:"AuditList",props:{auditType:{type:String,required:!0},listData:{type:Array,required:!0},extraFields:{type:Array,default:()=>[]},typeLabel:{type:String,required:!0},loading:{type:Boolean,default:!1}},data:()=>({showConfirmPopup:!1,confirmType:"",currentItem:null,rejectReason:""}),methods:{getStatusText:e=>"approved"===e?"已通过":"rejected"===e?"已拒绝":"待审核",getStatusType:e=>"approved"===e?"success":"rejected"===e?"error":"warning",formatDate:e=>D(e),showConfirmModal(e,t){this.currentItem=e,this.confirmType=t,this.rejectReason="",this.showConfirmPopup=!0},cancelConfirm(){this.showConfirmPopup=!1,this.currentItem=null,this.confirmType="",this.rejectReason=""},handleConfirm(){"approve"===this.confirmType?this.$emit("approve",this.currentItem):this.$emit("reject",{item:this.currentItem,reason:this.rejectReason}),this.showConfirmPopup=!1,this.currentItem=null,this.confirmType="",this.rejectReason=""},viewDetail(e){this.$emit("view-detail",e)},getRoleText:e=>({employee:"员工",agent:"代理",user:"用户"}[e]||"未知"),getFieldValue(e,t){if(t.path&&"string"==typeof t.path){const a=t.path.split(".");let s=e;for(const e of a){if(null==s)return t.defaultValue||"无";s=s[e]}return s||t.defaultValue||"无"}return t.defaultValue||"无"}}},[["render",function(e,b,L,V,D,R){const $=t(s("u-loadmore"),a),z=t(s("u-avatar"),i),A=_,I=t(s("u-tag"),l),F=t(s("u-cell"),r),P=t(s("u-cell-group"),o),S=t(s("u-icon"),n),U=t(s("u-button"),u),q=t(s("u-card"),d),B=t(s("u-empty"),c),M=j,H=t(s("u-input"),p),N=t(s("u-modal"),m);return f(),h(A,{class:"audit-container"},{default:y((()=>[L.loading?(f(),h($,{key:0,status:"loading",loadingText:"加载中..."})):(f(),h(A,{key:1,class:"audit-list"},{default:y((()=>[(f(!0),g(v,null,x(L.listData,((e,t)=>(f(),h(q,{key:e.id,padding:0,margin:"0 0 20rpx 0"},{head:y((()=>[T(A,{class:"card-header"},{default:y((()=>[T(A,{class:"user-basic-info"},{default:y((()=>[T(z,{src:e.avatar||"/static/images/avatar-placeholder.png",size:"40",shape:"square"},null,8,["src"]),T(A,{class:"user-info"},{default:y((()=>[T(A,{class:"user-name"},{default:y((()=>[k(w(e.username||e.name||"未知用户"),1)])),_:2},1024),T(I,{text:R.getRoleText(e.role||L.auditType),type:"info",size:"mini",plain:!0},null,8,["text"])])),_:2},1024)])),_:2},1024),T(I,{text:R.getStatusText(e.status),type:R.getStatusType(e.status),size:"mini"},null,8,["text","type"])])),_:2},1024)])),body:y((()=>[T(A,{class:"card-body"},{default:y((()=>[L.extraFields.length>0||e.applyTime||e.auditTime?(f(),h(P,{key:0,border:!1},{default:y((()=>[(f(!0),g(v,null,x(L.extraFields,((t,a)=>(f(),h(F,{key:a,title:t.label,value:R.getFieldValue(e,t),border:!1,size:"small"},null,8,["title","value"])))),128)),T(F,{title:"申请时间",value:R.formatDate(e.applyTime),border:!1,size:"small"},null,8,["value"]),"pending"!==e.status?(f(),h(F,{key:0,title:"审核时间",value:R.formatDate(e.auditTime),border:!1,size:"small"},null,8,["value"])):C("",!0)])),_:2},1024)):C("",!0)])),_:2},1024)])),foot:y((()=>[T(A,{class:"card-footer"},{default:y((()=>["pending"===e.status?(f(),g(v,{key:0},[T(U,{text:"拒绝",type:"info",plain:!0,size:"small",onClick:t=>R.showConfirmModal(e,"reject")},{icon:y((()=>[T(S,{name:"close",size:"14"})])),_:2},1032,["onClick"]),T(U,{text:"通过",type:"primary",size:"small",onClick:t=>R.showConfirmModal(e,"approve")},{icon:y((()=>[T(S,{name:"checkmark",size:"14"})])),_:2},1032,["onClick"])],64)):(f(),h(U,{key:1,text:"查看详情",type:"info",plain:!0,size:"small",onClick:t=>R.viewDetail(e)},{icon:y((()=>[T(S,{name:"eye",size:"14"})])),_:2},1032,["onClick"]))])),_:2},1024)])),_:2},1024)))),128)),0===L.listData.length?(f(),h(B,{key:0,mode:"data",text:`暂无${L.typeLabel}申请`},null,8,["text"])):C("",!0)])),_:1})),T(N,{modelValue:D.showConfirmPopup,"onUpdate:modelValue":b[1]||(b[1]=e=>D.showConfirmPopup=e),title:"确认"+("approve"===D.confirmType?"通过":"拒绝"),showCancelButton:!0,onConfirm:R.handleConfirm,onCancel:R.cancelConfirm},{default:y((()=>[T(A,{class:"modal-content"},{default:y((()=>[T(M,{class:"modal-text"},{default:y((()=>[k("确定要"+w("approve"===D.confirmType?"通过":"拒绝")+"该申请吗？",1)])),_:1}),"reject"===D.confirmType?(f(),h(H,{key:0,modelValue:D.rejectReason,"onUpdate:modelValue":b[0]||(b[0]=e=>D.rejectReason=e),placeholder:"请输入拒绝原因（可选）",type:"textarea",autoHeight:!0,maxlength:"200",style:{"margin-top":"20rpx"}},null,8,["modelValue"])):C("",!0)])),_:1})])),_:1},8,["modelValue","title","onConfirm","onCancel"])])),_:1})}],["__scopeId","data-v-35c32310"]])},data:()=>({userList:[],loading:!1,extraFields:[{label:"推荐人",path:"referrer",defaultValue:"无"},{label:"会员类型",path:"memberType",defaultValue:"普通用户"}]}),onLoad(){this.loadUserList()},methods:{async loadUserList(){try{this.loading=!0;const e=await R.get("/UserAudit/pending-users");e.success&&e.data?this.userList=e.data||[]:(this.$u.toast(e.msg||"获取用户列表失败"),this.userList=[])}catch(e){console.error("获取待审核用户列表失败:",e),this.$u.toast("网络错误，请稍后重试"),this.userList=[]}finally{this.loading=!1}},async handleReject(e){try{const{item:t,reason:a}=e,s=await function(e,t=""){return $(e,{status:2,remark:t})}(t.id,a||"无");if(s.success){const e=this.userList.findIndex((e=>e.id===t.id));-1!==e&&this.userList.splice(e,1),this.$u.toast("已拒绝申请")}else this.$u.toast(s.msg||"操作失败")}catch(t){console.error("拒绝审核出错:",t),this.$u.toast("网络错误，请稍后重试")}},async handleApprove(e){try{const t=await function(e,t=""){return $(e,{status:1,remark:t})}(e.id);if(t.success){const t=this.userList.findIndex((t=>t.id===e.id));-1!==t&&this.userList.splice(t,1),this.$u.toast("已通过申请")}else this.$u.toast(t.msg||"操作失败")}catch(t){console.error("通过审核出错:",t),this.$u.toast("网络错误，请稍后重试")}},viewDetail(e){b({url:`/pages/admin/users/info?userId=${e.id}&type=user`})}}},[["render",function(e,a,i,l,r,o){const n=t(s("u-navbar"),V),u=L("AuditList"),d=_;return f(),h(d,{class:"container"},{default:y((()=>[T(n,{title:"用户审核",autoBack:!0,placeholder:!0}),T(u,{auditType:"user",listData:r.userList,extraFields:r.extraFields,typeLabel:"用户",loading:r.loading,onReject:o.handleReject,onApprove:o.handleApprove,onViewDetail:o.viewDetail},null,8,["listData","extraFields","loading","onReject","onApprove","onViewDetail"])])),_:1})}],["__scopeId","data-v-d74f9a50"]]);export{z as default};
