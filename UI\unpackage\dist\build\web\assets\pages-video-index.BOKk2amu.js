import{_ as e,q as t,o as s,c as a,w as r,g as o,h as i,n,t as c,v as l,x as d,F as h,j as u,k as m,i as g,y as p,z as w,A as v,B as I,C as f,D as k,E as y,G as T,m as C,p as b,H as U,I as A,u as _,J as W,K as S,S as P,L as D}from"./index-qwAcQRkd.js";import{c as R,u as q}from"./view-record.DnCx8nLK.js";import{m as $}from"./media-common.BAxpCZbC.js";import{g as x}from"./adminAuthService.2Y4HA-uG.js";import{w as V}from"./video-user.DlhL-S2T.js";import"./format.t5pgP9mx.js";const L=e({name:"VideoQuiz",props:{questions:{type:Array,default:()=>[]},rewardAmount:{type:Number,default:0},videoCompleted:{type:Boolean,default:!1}},data:()=>({selectedAnswers:[],showResults:!1,correctCount:0,showResult:!1,earnedAmount:0}),computed:{canSubmit(){return!(!this.questions||0===this.questions.length)&&(this.selectedAnswers.length===this.questions.length&&!this.selectedAnswers.includes(void 0))}},watch:{questions:{immediate:!0,handler(e){e&&e.length>0&&(this.selectedAnswers=new Array(e.length))}}},methods:{selectAnswer(e,s){this.videoCompleted?this.showResults||(this.$set(this.selectedAnswers,e,s),this.canSubmit&&t({title:"已回答所有问题，可以提交",icon:"none",duration:1500})):t({title:"请先观看完视频",icon:"none"})},submitAnswers(){if(!this.videoCompleted)return void t({title:"请先观看完视频",icon:"none"});if(!this.canSubmit){const e=this.questions.length-this.selectedAnswers.filter(Boolean).length;return void t({title:`还有${e}个问题未回答`,icon:"none"})}this.correctCount=0,this.questions.forEach(((e,t)=>{this.selectedAnswers[t]===e.correctAnswer&&this.correctCount++}));const e=this.correctCount/this.questions.length;this.earnedAmount=(this.rewardAmount*e).toFixed(2),this.showResults=!0,this.$emit("submit",{answers:this.selectedAnswers,correctCount:this.correctCount,earnedAmount:this.earnedAmount}),setTimeout((()=>{this.showResult=!0}),1e3)},closeResult(){this.showResult=!1,this.$emit("complete")},reset(){this.selectedAnswers=new Array(this.questions.length),this.showResults=!1,this.correctCount=0,this.showResult=!1,this.earnedAmount=0}}},[["render",function(e,t,w,v,I,f){const k=m,y=g,T=p;return s(),a(y,{class:"quiz-box"},{default:r((()=>[o(y,{class:"quiz-header"},{default:r((()=>[o(k,{class:"quiz-title"},{default:r((()=>[i("视频问答")])),_:1}),o(k,{class:n(["quiz-note",{ready:w.videoCompleted}])},{default:r((()=>[i(c(w.videoCompleted?"(可以答题了)":"(请先观看完视频)"),1)])),_:1},8,["class"])])),_:1}),o(y,{class:"question-list"},{default:r((()=>[(s(!0),l(h,null,d(w.questions,((e,t)=>(s(),a(y,{class:"question-item",key:t},{default:r((()=>[o(y,{class:"question-text"},{default:r((()=>[o(k,{class:"q-number"},{default:r((()=>[i(c(t+1)+". ",1)])),_:2},1024),o(k,{class:"q-content"},{default:r((()=>[i(c(e.question),1)])),_:2},1024)])),_:2},1024),o(y,{class:"options"},{default:r((()=>[(s(!0),l(h,null,d(e.options,(l=>(s(),a(y,{class:n(["option",{selected:I.selectedAnswers[t]===l.id,correct:I.showResults&&l.id===e.correctAnswer,wrong:I.showResults&&I.selectedAnswers[t]===l.id&&l.id!==e.correctAnswer}]),key:l.id,onClick:e=>f.selectAnswer(t,l.id)},{default:r((()=>[o(k,{class:"option-label"},{default:r((()=>[i(c(l.id),1)])),_:2},1024),o(k,{class:"option-text"},{default:r((()=>[i(c(l.text),1)])),_:2},1024)])),_:2},1032,["class","onClick"])))),128))])),_:2},1024)])),_:2},1024)))),128))])),_:1}),o(T,{class:n(["submit-btn",{disabled:!f.canSubmit||!w.videoCompleted}]),disabled:!f.canSubmit||!w.videoCompleted,onClick:f.submitAnswers},{default:r((()=>[i(" 提交答案 ")])),_:1},8,["disabled","class","onClick"]),I.showResult?(s(),a(y,{key:0,class:"result-modal"},{default:r((()=>[o(y,{class:"result-content"},{default:r((()=>[o(y,{class:"result-header"},{default:r((()=>[o(k,{class:"result-title"},{default:r((()=>[i("答题结果")])),_:1})])),_:1}),o(k,{class:"result-score"},{default:r((()=>[i("得分: "+c(I.correctCount)+"/"+c(w.questions.length),1)])),_:1}),w.rewardAmount>0?(s(),a(k,{key:0,class:"result-reward"},{default:r((()=>[i("获得红包: "+c(I.earnedAmount)+"元",1)])),_:1})):u("",!0),o(T,{class:"close-btn",onClick:f.closeResult},{default:r((()=>[i("关闭")])),_:1},8,["onClick"])])),_:1})])),_:1})):u("",!0)])),_:1})}],["__scopeId","data-v-73977c8d"]]),E="wechatUserInfo",z="wechatUserToken";const O=new class{async wechatLogin(e){try{const t=await V(e);if(t.success&&t.data)return this.saveUserInfo(t.data.userInfo),this.saveUserToken(t.data.token),{success:!0,message:"微信登录成功",data:t.data};throw new Error(t.message||"微信登录失败")}catch(t){return console.error("微信登录失败:",t),{success:!1,message:t.message||"微信登录失败，请重试"}}}async tryWechatAutoLogin(e){try{throw new Error("微信登录仅支持在微信小程序中使用")}catch(t){throw console.error("微信自动登录失败:",t),t}}saveUserInfo(e){try{w(E,e)}catch(t){console.error("Error saving wechat user info:",t)}}saveUserToken(e){try{w(z,e)}catch(t){console.error("Error saving wechat user token:",t)}}getUserInfo(){try{return v(E)||null}catch(e){return console.error("Error getting wechat user info:",e),null}}getUserToken(){try{return v(z)||null}catch(e){return console.error("Error getting wechat user token:",e),null}}isLoggedIn(){const e=this.getUserInfo(),t=this.getUserToken();return!(!e||!t)}getUserId(){const e=this.getUserInfo();return e?e.id:null}getNickname(){const e=this.getUserInfo();return e?e.nickname:null}getAvatar(){const e=this.getUserInfo();return e?e.avatar:null}getOpenId(){const e=this.getUserInfo();return e?e.openId:null}getEmployeeId(){const e=this.getUserInfo();return e?e.employeeId:null}logout(){try{return I(E),I(z),console.log("微信用户已登出"),!0}catch(e){return console.error("Error during wechat user logout:",e),!1}}getDisplayInfo(){const e=this.getUserInfo();return e?{id:e.id,nickname:e.nickname||"微信用户",avatar:e.avatar||"/static/logo.png",openId:e.openId,employeeId:e.employeeId,userType:"wechat_user",createTime:e.createTime,lastLogin:e.lastLogin}:{nickname:"未登录",avatar:"/static/logo.png",userType:"guest"}}initMockWechatUser(){const e={id:"mock_wechat_user_001",nickname:"微信用户",avatar:"/static/logo.png",openId:"mock_openid_001",employeeId:null,createTime:(new Date).toISOString(),lastLogin:(new Date).toISOString()},t="mock_wechat_token_001";return this.saveUserInfo(e),this.saveUserToken(t),console.log("模拟微信用户初始化完成:",e.nickname),{userInfo:e,token:t}}redirectToLogin(e=""){const t=e?`/pages/user-login/index?returnUrl=${encodeURIComponent(e)}`:"/pages/user-login/index";f({url:t})}requireAuth(e={}){return!this.isLoggedIn()&&(console.log("微信用户未登录，需要认证"),!0)}};const j=e({mixins:[$],components:{VideoQuiz:L},data:()=>({showUserSelector:!1,selectedUserId:null,availableUsers:[{id:"1",nickname:"小明同学",avatar:"http://192.168.3.118/static/images/avatar-placeholder.png"},{id:"2",nickname:"阳光女孩",avatar:"http://192.168.3.118/static/images/avatar-placeholder.png"},{id:"3",nickname:"健康达人",avatar:"http://192.168.3.118/static/images/avatar-placeholder.png"},{id:"4",nickname:"学习小能手",avatar:"http://192.168.3.118/static/images/avatar-placeholder.png"},{id:"5",nickname:"运动爱好者",avatar:"http://192.168.3.118/static/images/avatar-placeholder.png"},{id:"9",nickname:"张小明",avatar:"/avatars/avatar1.jpg"},{id:"10",nickname:"李小红",avatar:"/avatars/avatar2.jpg"},{id:"11",nickname:"王小刚",avatar:"/avatars/avatar3.jpg"},{id:"12",nickname:"赵小美",avatar:"/avatars/avatar4.jpg"},{id:"13",nickname:"刘小强",avatar:"/avatars/avatar5.jpg"},{id:"14",nickname:"陈小丽",avatar:"/avatars/avatar6.jpg"},{id:"15",nickname:"周小华",avatar:"/avatars/avatar7.jpg"},{id:"16",nickname:"吴小军",avatar:"/avatars/avatar8.jpg"},{id:"17",nickname:"郑小芳",avatar:"/avatars/avatar9.jpg"},{id:"18",nickname:"孙小龙",avatar:"/avatars/avatar10.jpg"},{id:"20",nickname:"知识青年",avatar:"http://192.168.3.118/static/images/avatar-placeholder.png"},{id:"21",nickname:"安全专家",avatar:"http://192.168.3.118/static/images/avatar-placeholder.png"},{id:"22",nickname:"生活达人",avatar:"http://192.168.3.118/static/images/avatar-placeholder.png"},{id:"23",nickname:"健康守护者",avatar:"http://192.168.3.118/static/images/avatar-placeholder.png"},{id:"24",nickname:"防护专员",avatar:"http://192.168.3.118/static/images/avatar-placeholder.png"},{id:"25",nickname:"消毒小能手",avatar:"http://192.168.3.118/static/images/avatar-placeholder.png"},{id:"26",nickname:"卫生监督员",avatar:"http://192.168.3.118/static/images/avatar-placeholder.png"}],videoId:2,batchId:null,sharerId:null,currentVideo:{},quizData:{},duration:0,isFullscreen:!1,videoCompleted:!1,maxWatchTime:0,currentPlayTime:0,progressTimer:null,watchStartTime:null,totalWatchTime:0,viewRecordId:null,recordCreated:!1,isCreatingRecord:!1,batchInfo:null}),async onLoad(e){e&&0!==Object.keys(e).length||(e=this.parseUrlParams()||{});try{this.initMockWechatUser()}catch(t){console.error("模拟用户初始化失败:",t)}k({pageStyle:{overflow:"hidden"}}),e&&(e.id||e.videoId)&&(this.videoId=parseInt(e.id||e.videoId)),e&&e.batchId&&(this.batchId=parseInt(e.batchId)),e&&e.sharerId&&(this.sharerId=e.sharerId),this.showUserSelectorModal()},onShow(){this.batchId||this.parseUrlParams();const e=y("myVideo",this);e&&e.play(),this.startProgressMonitoring()},onHide(){const e=y("myVideo",this);e&&e.pause(),this.stopProgressMonitoring()},onUnload(){this.stopProgressMonitoring()},methods:{getLocalWatchRecord(){if(!this.batchId||!this.getCurrentUserId())return null;const e=`watch_record_${this.batchId}_${this.getCurrentUserId()}`,t=v(e);return t&&t.expireTime&&Date.now()>t.expireTime?(I(e),null):t},saveLocalWatchRecord(e){if(!this.batchId||!this.getCurrentUserId())return;const t=`watch_record_${this.batchId}_${this.getCurrentUserId()}`,s={id:e.id,batchId:this.batchId,userId:this.getCurrentUserId(),createTime:Date.now(),expireTime:Date.now()+864e5};w(t,s)},clearLocalWatchRecord(){if(!this.batchId||!this.getCurrentUserId())return;const e=`watch_record_${this.batchId}_${this.getCurrentUserId()}`;I(e)},getVideoDetailWithoutAuth:async e=>new Promise(((t,s)=>{T({url:`${x()}/Video/Get/${e}`,method:"GET",header:{"Content-Type":"application/json"},success:e=>{200===e.statusCode?t(e.data):s(new Error(`HTTP ${e.statusCode}`))},fail:e=>{s(e)}})})),getBatchDetailWithoutAuth:async e=>new Promise(((t,s)=>{T({url:`${x()}/Batch/${e}`,method:"GET",header:{"Content-Type":"application/json"},success:e=>{200===e.statusCode?t(e.data):s(new Error(`HTTP ${e.statusCode}`))},fail:e=>{s(e)}})})),getUserVideoProgressWithoutAuth:async()=>Promise.resolve({success:!0,data:{progress:0,playDuration:0,completed:!1},msg:"观看进度获取成功"}),createOrGetRecordWithoutAuth:async e=>new Promise(((t,s)=>{T({url:`${x()}/UserBatchRecord/create-or-get`,method:"POST",header:{"Content-Type":"application/json"},data:e,success:e=>{200===e.statusCode?t(e.data):s(new Error(`HTTP ${e.statusCode}`))},fail:e=>{s(e)}})})),updateWatchProgressWithoutAuth:async(e,t)=>new Promise(((s,a)=>{T({url:`${x()}/UserBatchRecord/${e}/watch-progress`,method:"POST",header:{"Content-Type":"application/json"},data:t,success:e=>{200===e.statusCode?s(e.data):a(new Error(`HTTP ${e.statusCode}`))},fail:e=>{a(e)}})})),startWatchingWithoutAuth:async(e,t)=>new Promise(((s,a)=>{T({url:`${x()}/UserBatchRecord/${e}/${t}/start-watching`,method:"POST",header:{"Content-Type":"application/json"},success:e=>{200===e.statusCode?s(e.data):a(new Error(`HTTP ${e.statusCode}`))},fail:e=>{a(e)}})})),submitAnswerWithoutAuth:async(e,t)=>new Promise(((s,a)=>{T({url:`${x()}/UserBatchRecord/${e}/submit-answer`,method:"POST",header:{"Content-Type":"application/json"},data:t,success:e=>{200===e.statusCode?s(e.data):a(new Error(`HTTP ${e.statusCode}`))},fail:e=>{a(e)}})})),grantRewardWithoutAuth:async(e,t)=>new Promise(((s,a)=>{T({url:`${x()}/UserBatchRecord/${e}/grant-reward`,method:"POST",header:{"Content-Type":"application/json"},data:t,success:e=>{200===e.statusCode?s(e.data):a(new Error(`HTTP ${e.statusCode}`))},fail:e=>{a(e)}})})),async tryWechatAutoLogin(e){try{const t=await O.tryWechatAutoLogin(e);if(t.success)return console.log("微信自动登录成功:",t.data.userInfo.nickname),t.data;throw new Error(t.message||"微信登录失败")}catch(t){throw console.error("微信自动登录失败:",t),t}},async loadVideoData(){try{C({title:"加载视频中..."}),this.batchId?await this.loadDataFromBatch():await this.loadDataFromVideo(),await this.loadUserProgress(),this.sharerId&&this.recordSharerInfo(),b()}catch(e){console.error("加载视频数据失败:",e),b(),t({title:e.message||"加载失败",icon:"none"}),setTimeout((()=>{U()}),1500)}},async loadDataFromBatch(){console.log("从批次获取数据:",this.batchId);const e=await this.getBatchDetailWithoutAuth(this.batchId);if(!e.success||!e.data)throw new Error(e.msg||"获取批次详情失败");const t=e.data;console.log("=== 批次数据调试信息 ==="),console.log("API返回的原始批次数据:",t),this.validateBatchStatus(t),this.batchInfo=t;const s=this.buildCompleteFileUrl(t.videoUrl),a=this.buildCompleteFileUrl(t.videoCoverUrl);this.currentVideo={id:t.videoId,title:t.videoTitle,cover:a||"/assets/images/video-cover.jpg",url:s||"https://www.runoob.com/try/demo_source/mov_bbb.mp4",duration:t.videoDuration||0,views:0,likes:0,description:t.videoDescription||"",rewardAmount:t.rewardAmount||0},this.duration=t.videoDuration||0,this.processQuizData(t.questions),console.log("最终设置的currentVideo:",this.currentVideo),console.log("最终设置的quizData:",this.quizData),console.log("=== 批次数据调试结束 ===")},async loadDataFromVideo(){console.log("从视频API获取数据:",this.videoId);const e=await this.getVideoDetailWithoutAuth(this.videoId);if(!e.success||!e.data)throw new Error(e.msg||"获取视频详情失败");const t=e.data;console.log("=== 视频数据调试信息 ==="),console.log("API返回的原始视频数据:",t);const s=this.buildCompleteFileUrl(t.videoUrl),a=this.buildCompleteFileUrl(t.coverUrl);this.currentVideo={id:t.id,title:t.title,cover:a||"/assets/images/video-cover.jpg",url:s||"https://www.runoob.com/try/demo_source/mov_bbb.mp4",duration:t.duration||0,views:t.viewCount||0,likes:t.likeCount||0,description:t.description||"",rewardAmount:t.rewardAmount||0},this.duration=t.duration||0,this.processQuizData(t.questions),console.log("最终设置的currentVideo:",this.currentVideo),console.log("最终设置的quizData:",this.quizData),console.log("=== 视频数据调试结束 ===")},async loadUserProgress(){try{const e=await this.getUserVideoProgressWithoutAuth();if(e.success&&e.data){const t=e.data;this.totalWatchTime=t.playDuration||0,this.maxWatchTime=this.duration>0?Math.round(t.progress/100*this.duration):0,this.maxWatchTime>0&&A({title:"继续观看",content:`检测到您上次观看到 ${this.formatTime(this.maxWatchTime)}，是否从此处继续？`,success:e=>{e.confirm&&setTimeout((()=>{const e=y("myVideo",this);e&&e.seek(this.maxWatchTime)}),1e3)}})}}catch(e){console.error("加载用户进度失败:",e)}},processQuizData(e){if(!e||!Array.isArray(e)||0===e.length)return console.log("没有问题数据"),void(this.quizData={questions:[]});console.log("开始处理问题数据:",e);try{const t=e.map((e=>{const t=["A","B","C","D","E","F"],s=(e.options||[]).map(((e,s)=>({id:t[s]||s.toString(),text:e.optionText||e.text||""})));let a="";const r=(e.options||[]).findIndex((e=>e.isCorrect));return r>=0&&(a=t[r]||r.toString()),{question:e.questionText||e.question||"",options:s,correctAnswer:a}}));this.quizData={questions:t},console.log("问题数据处理完成:",this.quizData)}catch(t){console.error("处理问题数据失败:",t),this.quizData={questions:[]}}},validateBatchStatus(e){if(1!==e.status)throw new Error("批次未启用");const t=new Date,s=new Date(e.startTime),a=new Date(e.endTime);if(t<s)throw new Error("批次尚未开始");if(t>a)throw new Error("批次已结束");console.log("批次验证通过:",e)},formatTime:e=>`${Math.floor(e/60)}:${Math.floor(e%60).toString().padStart(2,"0")}`,onTimeUpdate(e){const s=e.detail||{},a=s.currentTime||0,r=s.duration||this.duration||0;if(this.currentPlayTime=a,r>0&&(this.duration=r),a>this.maxWatchTime+1){const e=y("myVideo",this);return e&&e.seek(this.maxWatchTime),void t({title:"请完整观看视频，不允许快进",icon:"none",duration:2e3})}a>this.maxWatchTime&&(this.maxWatchTime=a);const o={current:a,duration:r};this.reportProgress&&this.reportProgress(o)},onVideoEnded(){this.videoCompleted=!0,this.maxWatchTime=Number.MAX_VALUE,t({title:"视频播放完成，可以答题了",icon:"none",duration:2e3})},onSeeking(e){(e.detail.currentTime||0)>this.maxWatchTime+1&&(setTimeout((()=>{const e=y("myVideo",this);e&&e.seek(this.maxWatchTime)}),100),t({title:"不允许快进，请完整观看",icon:"none",duration:2e3}))},onSeeked(e){if((e.detail.currentTime||0)>this.maxWatchTime+1){const e=y("myVideo",this);e&&e.seek(this.maxWatchTime),t({title:"已回退到正确位置",icon:"none",duration:1500})}},onPlay(){this.watchStartTime=Date.now()},onPause(){if(this.watchStartTime){const e=(Date.now()-this.watchStartTime)/1e3;this.totalWatchTime+=e,this.watchStartTime=null}},onLoadedMetadata(){},onWaiting(){},onCanPlay(){},onFullscreenChange(e){this.isFullscreen=e.detail.fullScreen,this.isFullscreen?this.lockOrientation():this.unlockOrientation()},async lockOrientation(){try{if(screen.orientation&&screen.orientation.lock)return void(await screen.orientation.lock("landscape"));if(screen.lockOrientation)return void screen.lockOrientation("landscape");if(screen.webkitLockOrientation)return void screen.webkitLockOrientation("landscape");if(screen.mozLockOrientation)return void screen.mozLockOrientation("landscape")}catch(e){}},unlockOrientation(){try{if(screen.orientation&&screen.orientation.unlock)return void screen.orientation.unlock();if(screen.unlockOrientation)return void screen.unlockOrientation();if(screen.webkitUnlockOrientation)return void screen.webkitUnlockOrientation();if(screen.mozUnlockOrientation)return void screen.mozUnlockOrientation()}catch(e){}},async onQuizSubmit(e){try{if(!this.batchId)return void console.warn("缺少批次ID，无法提交答案");console.log("提交答案:",e);const s=await this.submitAnswerWithoutAuth(this.getCurrentUserId(),{batchId:this.batchId,answers:e.answers,score:e.score,isCorrect:e.isCorrect,completedAt:(new Date).toISOString()});s.success?(console.log("答案提交成功:",s.data),t({title:"答案提交成功",icon:"success"})):(console.error("答案提交失败:",s.msg),t({title:"答案提交失败",icon:"none"}))}catch(s){console.error("提交答案失败:",s),t({title:"提交失败，请重试",icon:"none"})}},async onQuizComplete(e){try{if(console.log("答题完成，奖励信息:",e),e&&e.rewardAmount>0){const s=await this.grantRewardWithoutAuth(this.getCurrentUserId(),{batchId:this.batchId,rewardAmount:e.rewardAmount,rewardType:"quiz_completion",description:"完成视频答题奖励"});s.success?(console.log("奖励发放成功:",s.data),t({title:`恭喜获得 ${e.rewardAmount} 元奖励！`,icon:"success",duration:3e3})):console.error("奖励发放失败:",s.msg)}else t({title:"答题完成！",icon:"success",duration:2e3});console.log("答题完成，用户可以继续观看视频或手动返回")}catch(s){console.error("处理答题完成失败:",s),t({title:"答题完成处理失败",icon:"none",duration:2e3})}},startProgressMonitoring(){this.progressTimer&&clearInterval(this.progressTimer),this.watchStartTime=Date.now();const e=this.getLocalWatchRecord();this.recordCreated||e?e&&(this.recordCreated=!0,this.viewRecordId=e.id):this.createWatchRecord(),this.progressTimer=setInterval((()=>{this.outputWatchProgress()}),5e3)},stopProgressMonitoring(){if(this.watchStartTime){const e=(Date.now()-this.watchStartTime)/1e3;this.totalWatchTime+=e,this.watchStartTime=null}this.progressTimer&&(clearInterval(this.progressTimer),this.progressTimer=null),this.submitWatchProgress()},outputWatchProgress(){if(this.watchStartTime){const e=Date.now(),t=(e-this.watchStartTime)/1e3;this.totalWatchTime+=t,this.watchStartTime=e}this.submitWatchProgress()},async createWatchRecord(){if(!this.batchId)return;if(this.isCreatingRecord)return;const e=this.getLocalWatchRecord();if(e&&e.batchId===this.batchId)return this.recordCreated=!0,void(this.viewRecordId=e.id);if(!this.recordCreated||!this.viewRecordId){this.isCreatingRecord=!0;try{let e="";this.sharerId&&(e=`shared_by_${this.sharerId}`);const t=await this.createOrGetRecordWithoutAuth({batchId:this.batchId,userId:this.getCurrentUserId(),promotionLink:e});if(t.success&&t.data)return this.viewRecordId=t.data.id,this.recordCreated=!0,this.saveLocalWatchRecord(t.data),void(await this.startWatchingWithoutAuth(this.getCurrentUserId(),this.batchId))}catch(t){}try{const e=await R({batchId:this.batchId,userId:this.getCurrentUserId(),promotionLink:""});e.success&&e.data&&(this.viewRecordId=e.data,this.recordCreated=!0,this.saveLocalWatchRecord({id:e.data}))}catch(t){}finally{this.isCreatingRecord=!1}}},async submitWatchProgress(){if(this.batchId){try{let e=0;this.duration>0&&this.maxWatchTime>=0&&(e=Math.min(this.maxWatchTime/this.duration,1));if((await this.updateWatchProgressWithoutAuth(this.getCurrentUserId(),{batchId:this.batchId,viewDuration:Math.floor(this.totalWatchTime),watchProgress:e,isCompleted:this.videoCompleted})).success)return}catch(e){console.error("新API更新观看进度失败，尝试旧API:",e)}try{let e=0;this.duration>0&&this.maxWatchTime>=0&&(e=Math.min(this.maxWatchTime/this.duration,1));const t=await q({batchId:this.batchId,viewDuration:Math.floor(this.totalWatchTime),watchProgress:e,isCompleted:this.videoCompleted});t.success||console.error("更新观看进度失败:",t.msg)}catch(e){console.error("提交观看进度失败:",e)}}else console.warn("缺少批次ID，无法更新观看进度")},getCurrentUserId(){if(this.selectedUserId)return this.selectedUserId;if(this.availableUsers.length>0){const e=Math.floor(Math.random()*this.availableUsers.length);return this.selectedUserId=this.availableUsers[e].id,console.log("随机选择用户:",this.selectedUserId,this.availableUsers[e].nickname),this.selectedUserId}return"1"},showUserSelectorModal(){this.showUserSelector=!0},closeUserSelector(){this.showUserSelector=!1},selectUser(e){this.selectedUserId=e.id,this.showUserSelector=!1,console.log("选择用户:",e.nickname,"ID:",e.id),t({title:`已选择: ${e.nickname}`,icon:"success"}),this.loadVideoData(),this.startProgressMonitoring()},selectRandomUser(){if(this.availableUsers.length>0){const e=Math.floor(Math.random()*this.availableUsers.length),t=this.availableUsers[e];this.selectUser(t)}},initMockWechatUser(){const e=O.initMockWechatUser();console.log("模拟微信用户初始化完成:",e.userInfo.nickname)},parseUrlParams(){try{const e=window.location.href.split("#")[1];if(e&&e.includes("?")){const t=e.split("?")[1],s=new URLSearchParams(t),a={};for(let[e,r]of s)a[e]=r;return a.videoId&&(this.videoId=parseInt(a.videoId)),a.batchId&&(this.batchId=parseInt(a.batchId)),a.sharerId&&(this.sharerId=a.sharerId),a}return null}catch(e){return console.error("解析URL参数失败:",e),null}},recordSharerInfo(){try{console.log("记录分享人信息:",this.sharerId),w("currentSharerInfo",{sharerId:this.sharerId,batchId:this.batchId,videoId:this.videoId,shareTime:(new Date).toISOString()})}catch(e){console.error("记录分享人信息失败:",e)}}}},[["render",function(e,t,n,w,v,I){const f=S,k=m,y=g,T=_("VideoQuiz"),C=D,b=P,U=p;return s(),a(y,{class:"container"},{default:r((()=>[o(f,{id:"myVideo",ref:"videoPlayer",src:v.currentVideo.url,poster:v.currentVideo.cover,class:"video-player",controls:!0,autoplay:!0,"show-progress":!0,"show-fullscreen-btn":!0,"show-play-btn":!0,"show-center-play-btn":!0,"enable-progress-gesture":!1,"page-gesture":!1,direction:0,"show-mute-btn":!1,"enable-play-gesture":!1,onLoadedmetadata:I.onLoadedMetadata,onTimeupdate:I.onTimeUpdate,onEnded:I.onVideoEnded,onPlay:I.onPlay,onPause:I.onPause,onFullscreenchange:I.onFullscreenChange,onWaiting:I.onWaiting,onCanplay:I.onCanPlay,onSeeking:I.onSeeking,onSeeked:I.onSeeked},null,8,["src","poster","onLoadedmetadata","onTimeupdate","onEnded","onPlay","onPause","onFullscreenchange","onWaiting","onCanplay","onSeeking","onSeeked"]),v.isFullscreen?u("",!0):(s(),a(y,{key:0,class:"video-content"},{default:r((()=>[o(y,{class:"video-info"},{default:r((()=>[o(k,{class:"video-title"},{default:r((()=>[i(c(v.currentVideo.title),1)])),_:1}),o(y,{class:"video-meta"},{default:r((()=>[o(k,{class:"views"},{default:r((()=>[i(c(v.currentVideo.views)+"次观看",1)])),_:1})])),_:1}),o(k,{class:"description"},{default:r((()=>[i(c(v.currentVideo.description),1)])),_:1})])),_:1}),o(T,{questions:v.quizData.questions,rewardAmount:v.currentVideo.rewardAmount||0,videoCompleted:v.videoCompleted,onSubmit:I.onQuizSubmit,onComplete:I.onQuizComplete},null,8,["questions","rewardAmount","videoCompleted","onSubmit","onComplete"])])),_:1})),v.showUserSelector?(s(),a(y,{key:1,class:"user-selector-modal",onClick:I.closeUserSelector},{default:r((()=>[o(y,{class:"user-selector-content",onClick:t[0]||(t[0]=W((()=>{}),["stop"]))},{default:r((()=>[o(y,{class:"selector-header"},{default:r((()=>[o(k,{class:"selector-title"},{default:r((()=>[i("选择测试用户")])),_:1}),o(k,{class:"selector-subtitle"},{default:r((()=>[i("请选择一个用户身份进行测试")])),_:1})])),_:1}),o(b,{class:"user-list","scroll-y":""},{default:r((()=>[(s(!0),l(h,null,d(v.availableUsers,(e=>(s(),a(y,{class:"user-item",key:e.id,onClick:t=>I.selectUser(e)},{default:r((()=>[o(C,{class:"user-avatar",src:e.avatar,mode:"aspectFill"},null,8,["src"]),o(y,{class:"user-info"},{default:r((()=>[o(k,{class:"user-nickname"},{default:r((()=>[i(c(e.nickname),1)])),_:2},1024),o(k,{class:"user-id"},{default:r((()=>[i("ID: "+c(e.id),1)])),_:2},1024)])),_:2},1024),o(y,{class:"select-icon"},{default:r((()=>[o(k,{class:"icon"},{default:r((()=>[i(">")])),_:1})])),_:1})])),_:2},1032,["onClick"])))),128))])),_:1}),o(y,{class:"selector-footer"},{default:r((()=>[o(U,{class:"random-btn",onClick:I.selectRandomUser},{default:r((()=>[i("随机选择")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1},8,["onClick"])):u("",!0)])),_:1})}],["__scopeId","data-v-2dddd1a8"]]);export{j as default};
