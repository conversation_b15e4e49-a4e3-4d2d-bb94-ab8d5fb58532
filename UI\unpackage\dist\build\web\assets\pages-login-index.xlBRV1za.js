import{_ as s,r as e,a,c as o,w as r,n as t,i,b as n,d as l,e as d,f as u,o as c,g as m,h as g,t as h,j as p,k as w}from"./index-qwAcQRkd.js";import{a as f}from"./adminAuthService.2Y4HA-uG.js";const _=s({data:()=>({loginForm:{username:"",password:""},showPassword:!1,isLoading:!1,pageLoaded:!1,errors:{username:"",password:""}}),computed:{canSubmit(){return this.loginForm.username.trim()&&this.loginForm.password.trim()&&!this.isLoading}},onLoad(){this.checkExistingLogin(),setTimeout((()=>{this.pageLoaded=!0}),100)},methods:{resetForm(){this.loginForm={username:"",password:""},this.errors={username:"",password:""},this.showPassword=!1,this.isLoading=!1},togglePassword(){this.showPassword=!this.showPassword},validateUsername(){const s=this.loginForm.username.trim();return s?s.length<3?(this.errors.username="用户名至少3个字符",!1):/^[a-zA-Z0-9_]+$/.test(s)?(this.errors.username="",!0):(this.errors.username="用户名只能包含字母、数字和下划线",!1):(this.errors.username="请输入用户名",!1)},validatePassword(){const s=this.loginForm.password.trim();return s?s.length<6?(this.errors.password="密码至少6个字符",!1):(this.errors.password="",!0):(this.errors.password="请输入密码",!1)},clearError(s){this.errors[s]&&(this.errors[s]="")},validateForm(){const s=this.validateUsername(),e=this.validatePassword();return s&&e},async handleLogin(){if(this.validateForm()){if(this.canSubmit&&!this.isLoading){this.isLoading=!0;try{const{username:s,password:e}=this.loginForm,a=await f.authenticate(s,e);a.success?(this.showToastMessage(a.message,"success"),await this.delay(1e3),f.redirectToMain()):(this.showToastMessage(a.message,"error"),this.loginForm.password="")}catch(s){console.error("Login error:",s),this.showToastMessage("登录失败，请重试","error"),this.loginForm.password=""}finally{this.isLoading=!1}}}else this.showToastMessage("请检查输入信息","error")},checkExistingLogin(){f.isLoggedIn()&&f.isSessionValid()?f.redirectToMain():f.isLoggedIn()&&f.logout()},showToastMessage(s,e="success"){this.$refs.uToast.show({message:s,type:e,duration:3e3})},delay:s=>new Promise((e=>setTimeout(e,s))),quickLogin(s,e){this.isLoading||(this.loginForm.username=s,this.loginForm.password=e,this.errors.username="",this.errors.password="",this.handleLogin())}}},[["render",function(s,f,_,L,b,F){const k=w,y=i,P=e(a("u-input"),n),v=e(a("u-icon"),l),T=e(a("u-button"),d),x=e(a("u-toast"),u);return c(),o(y,{class:t(["login-container",{"page-loaded":b.pageLoaded}])},{default:r((()=>[m(y,{class:"login-content"},{default:r((()=>[m(y,{class:t(["header-section",{"animate-in":b.pageLoaded}])},{default:r((()=>[m(k,{class:"app-title"},{default:r((()=>[g("视频答题系统")])),_:1}),m(y,{class:"title-decoration"})])),_:1},8,["class"]),m(y,{class:t(["form-container",{"animate-in":b.pageLoaded}])},{default:r((()=>[m(y,{class:t(["input-group",{"animate-in":b.pageLoaded}])},{default:r((()=>[m(P,{modelValue:b.loginForm.username,"onUpdate:modelValue":f[0]||(f[0]=s=>b.loginForm.username=s),placeholder:"请输入用户名",border:"surround",clearable:"",error:!!b.errors.username,onBlur:F.validateUsername,onInput:f[1]||(f[1]=s=>F.clearError("username")),class:"login-input"},null,8,["modelValue","error","onBlur"]),b.errors.username?(c(),o(k,{key:0,class:"error-message animate-shake"},{default:r((()=>[g(h(b.errors.username),1)])),_:1})):p("",!0)])),_:1},8,["class"]),m(y,{class:t(["input-group",{"animate-in":b.pageLoaded}])},{default:r((()=>[m(P,{modelValue:b.loginForm.password,"onUpdate:modelValue":f[2]||(f[2]=s=>b.loginForm.password=s),placeholder:"请输入密码",border:"surround",clearable:"",password:!b.showPassword,error:!!b.errors.password,onBlur:F.validatePassword,onInput:f[3]||(f[3]=s=>F.clearError("password")),class:"login-input"},{suffix:r((()=>[m(v,{name:b.showPassword?"eye":"eye-off",size:"20",color:"#186BFF",onClick:F.togglePassword,class:"password-toggle"},null,8,["name","onClick"])])),_:1},8,["modelValue","password","error","onBlur"]),b.errors.password?(c(),o(k,{key:0,class:"error-message animate-shake"},{default:r((()=>[g(h(b.errors.password),1)])),_:1})):p("",!0)])),_:1},8,["class"]),m(T,{type:"primary",text:b.isLoading?"登录中...":"登录",loading:b.isLoading,disabled:!F.canSubmit||b.isLoading,onClick:F.handleLogin,class:t(["login-btn",{"animate-in":b.pageLoaded,"btn-loading":b.isLoading}])},null,8,["text","loading","disabled","onClick","class"]),m(y,{class:"test-accounts"},{default:r((()=>[m(k,{class:"test-title"},{default:r((()=>[g("测试账号")])),_:1}),m(y,{class:"test-grid"},{default:r((()=>[m(T,{type:"info",size:"small",disabled:b.isLoading,onClick:f[4]||(f[4]=s=>F.quickLogin("super_admin_001","123456")),class:"test-btn test-btn-first"},{default:r((()=>[m(y,{class:"test-btn-content"},{default:r((()=>[m(k,{class:"test-username"},{default:r((()=>[g("super_admin_001")])),_:1}),m(k,{class:"test-role"},{default:r((()=>[g("超管")])),_:1})])),_:1})])),_:1},8,["disabled"]),m(T,{type:"info",size:"small",disabled:b.isLoading,onClick:f[5]||(f[5]=s=>F.quickLogin("admin","123456")),class:"test-btn test-btn-middle"},{default:r((()=>[m(y,{class:"test-btn-content"},{default:r((()=>[m(k,{class:"test-username"},{default:r((()=>[g("admin")])),_:1}),m(k,{class:"test-role"},{default:r((()=>[g("管理")])),_:1})])),_:1})])),_:1},8,["disabled"]),m(T,{type:"info",size:"small",disabled:b.isLoading,onClick:f[6]||(f[6]=s=>F.quickLogin("emp1","123456")),class:"test-btn test-btn-last"},{default:r((()=>[m(y,{class:"test-btn-content"},{default:r((()=>[m(k,{class:"test-username"},{default:r((()=>[g("emp1")])),_:1}),m(k,{class:"test-role"},{default:r((()=>[g("员工1")])),_:1})])),_:1})])),_:1},8,["disabled"])])),_:1})])),_:1})])),_:1},8,["class"]),m(y,{class:"footer"},{default:r((()=>[m(k,{class:"footer-text"},{default:r((()=>[g("© 2024 视频答题系统")])),_:1}),m(k,{class:"footer-version"},{default:r((()=>[g("Version 1.0.0")])),_:1})])),_:1})])),_:1}),m(x,{ref:"uToast"},null,512)])),_:1},8,["class"])}],["__scopeId","data-v-8e1b24d0"]]);export{_ as default};
