import{_ as e,z as s,N as r,A as a,C as i,O as o,r as t,a as n,c as l,w as c,i as d,b as u,e as m,f as g,o as h,g as f,h as b,t as k,j as p,k as _}from"./index-qwAcQRkd.js";import{a as L}from"./video-user.DlhL-S2T.js";import"./adminAuthService.2Y4HA-uG.js";const w=e({data:()=>({loginForm:{nickname:"",mobile:""},isLoading:!1,errors:{nickname:"",mobile:""}}),computed:{canSubmit(){return this.loginForm.nickname.trim()&&!this.isLoading}},onLoad(e){this.checkExistingLogin(),e.returnUrl&&(this.returnUrl=decodeURIComponent(e.returnUrl))},methods:{validateNickname(){return this.loginForm.nickname.trim()?(this.errors.nickname="",!0):(this.errors.nickname="昵称不能为空",!1)},validateMobile(){const e=this.loginForm.mobile.trim();return e&&!/^1[3-9]\d{9}$/.test(e)?(this.errors.mobile="请输入正确的手机号",!1):(this.errors.mobile="",!0)},clearError(e){this.errors[e]=""},validateForm(){const e=this.validateNickname(),s=this.validateMobile();return e&&s},async handleLogin(){if(this.validateForm()){if(this.canSubmit&&!this.isLoading){this.isLoading=!0;try{const{nickname:e,mobile:r}=this.loginForm,a=await L({nickname:e.trim(),mobile:r.trim()||void 0});a.success&&a.data?(s("userInfo",a.data.userInfo),s("token",a.data.token),this.showToastMessage("登录成功！","success"),await this.delay(1500),this.redirectAfterLogin()):this.showToastMessage(a.msg||"登录失败","error")}catch(e){console.error("Login error:",e),this.showToastMessage("登录失败，请重试","error")}finally{this.isLoading=!1}}}else this.showToastMessage("请检查输入信息","error")},async wechatLogin(){try{this.isLoading=!0,this.showToastMessage("微信登录仅支持在微信小程序中使用","warning")}catch(e){console.error("微信登录失败:",e),this.showToastMessage(e.message||"微信登录失败，请重试","error")}finally{this.isLoading=!1}},goToRegister(){r({url:"/pages/register/index"})},checkExistingLogin(){const e=a("userInfo"),s=a("token");e&&s&&this.redirectAfterLogin()},redirectAfterLogin(){this.returnUrl?i({url:this.returnUrl}):o({url:"/pages/index/index"})},showToastMessage(e,s="success"){this.$refs.uToast.show({message:e,type:s,duration:3e3})},delay:e=>new Promise((s=>setTimeout(s,e)))}},[["render",function(e,s,r,a,i,o){const L=_,w=d,v=t(n("u-input"),u),x=t(n("u-button"),m),y=t(n("u-toast"),g);return h(),l(w,{class:"login-container"},{default:c((()=>[f(w,{class:"login-content"},{default:c((()=>[f(w,{class:"header-section"},{default:c((()=>[f(L,{class:"app-title"},{default:c((()=>[b("用户登录")])),_:1}),f(L,{class:"app-subtitle"},{default:c((()=>[b("登录您的账号继续观看")])),_:1})])),_:1}),f(w,{class:"form-container"},{default:c((()=>[f(w,{class:"input-group"},{default:c((()=>[f(L,{class:"input-label"},{default:c((()=>[b("昵称")])),_:1}),f(v,{modelValue:i.loginForm.nickname,"onUpdate:modelValue":s[0]||(s[0]=e=>i.loginForm.nickname=e),placeholder:"请输入您的昵称",border:"surround",clearable:"",error:!!i.errors.nickname,onBlur:o.validateNickname,onInput:s[1]||(s[1]=e=>o.clearError("nickname")),class:"login-input"},null,8,["modelValue","error","onBlur"]),i.errors.nickname?(h(),l(L,{key:0,class:"error-message"},{default:c((()=>[b(k(i.errors.nickname),1)])),_:1})):p("",!0)])),_:1}),f(w,{class:"input-group"},{default:c((()=>[f(L,{class:"input-label"},{default:c((()=>[b("手机号（可选）")])),_:1}),f(v,{modelValue:i.loginForm.mobile,"onUpdate:modelValue":s[2]||(s[2]=e=>i.loginForm.mobile=e),placeholder:"请输入手机号",border:"surround",clearable:"",error:!!i.errors.mobile,onBlur:o.validateMobile,onInput:s[3]||(s[3]=e=>o.clearError("mobile")),class:"login-input"},null,8,["modelValue","error","onBlur"]),i.errors.mobile?(h(),l(L,{key:0,class:"error-message"},{default:c((()=>[b(k(i.errors.mobile),1)])),_:1})):p("",!0)])),_:1}),f(x,{type:"primary",text:i.isLoading?"登录中...":"登录",loading:i.isLoading,disabled:!o.canSubmit||i.isLoading,onClick:o.handleLogin,class:"login-btn"},null,8,["text","loading","disabled","onClick"]),f(w,{class:"register-link-container"},{default:c((()=>[f(L,{class:"register-link-text"},{default:c((()=>[b("还没有账号？")])),_:1}),f(L,{class:"register-link",onClick:o.goToRegister},{default:c((()=>[b("立即注册")])),_:1},8,["onClick"])])),_:1}),f(w,{class:"divider"},{default:c((()=>[f(L,{class:"divider-text"},{default:c((()=>[b("或")])),_:1})])),_:1}),f(x,{type:"success",text:"微信登录",onClick:o.wechatLogin,class:"wechat-btn",disabled:i.isLoading,icon:"weixin"},null,8,["onClick","disabled"])])),_:1}),f(w,{class:"footer"},{default:c((()=>[f(L,{class:"footer-text"},{default:c((()=>[b("© 2024 视频分享系统")])),_:1}),f(L,{class:"footer-version"},{default:c((()=>[b("Version 1.0.0")])),_:1})])),_:1})])),_:1}),f(y,{ref:"uToast"},null,512)])),_:1})}],["__scopeId","data-v-c19d2d3c"]]);export{w as default};
