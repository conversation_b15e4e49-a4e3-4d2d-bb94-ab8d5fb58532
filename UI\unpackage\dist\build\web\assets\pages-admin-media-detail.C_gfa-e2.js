import{_ as e,q as t,m as o,p as s,N as i,H as a,ah as l,r as n,a as c,c as d,w as r,ai as u,i as p,Q as f,e as h,aj as m,ak as _,a5 as v,o as w,g,h as x,t as y,j as I,v as z,x as C,F as k,k as q,K as A,n as b}from"./index-qwAcQRkd.js";import{g as S,d as D}from"./video.DVRJ2aPj.js";import{m as T}from"./media-common.BAxpCZbC.js";import"./adminAuthService.2Y4HA-uG.js";import"./format.t5pgP9mx.js";const P=e({mixins:[T],data:()=>({videoId:0,videoInfo:{},quizzes:[],showDeletePopup:!1,showActionPopup:!1,actionList:[{name:"编辑视频",icon:"edit-pen",color:"#3c9cff"},{name:"创建批次",icon:"plus-circle",color:"#5ac725"},{name:"删除视频",icon:"trash",color:"#f56c6c"}]}),computed:{quizCount(){return this.quizzes.length},totalReward(){return this.videoInfo.rewardAmount||0},rewardText(){return"奖励: "+this.totalReward+"元"}},onLoad(e){if(e.id){if(this.videoId=parseInt(e.id),isNaN(this.videoId))return t({title:"视频ID无效",icon:"none"}),void setTimeout((()=>{this.goBack()}),1500);this.loadVideoInfo()}else t({title:"参数错误",icon:"none"}),setTimeout((()=>{this.goBack()}),1500)},onShow(){this.videoId&&this.loadVideoInfo()},methods:{async loadVideoInfo(){try{o({title:"加载中..."});const e=await S(this.videoId);if(!e.success||!e.data)throw new Error(e.msg||"获取视频详情失败");{const t=e.data;this.videoInfo={id:t.id,title:t.title,cover:this.buildCompleteFileUrl(t.coverUrl)||"/assets/images/video-cover.jpg",url:this.buildCompleteFileUrl(t.videoUrl)||"https://www.runoob.com/try/demo_source/mov_bbb.mp4",duration:this.formatDuration(t.duration),uploadTime:t.createTime,uploader:t.creatorName||"未知",uploaderId:t.creatorId||"",views:t.viewCount||0,likes:t.likeCount||0,description:t.description||"",status:this.mapVideoStatus(t.status),rewardAmount:t.rewardAmount||0},this.processQuestions(t.questions||[])}s()}catch(e){console.error("加载视频详情失败:",e),s(),t({title:"加载视频详情失败",icon:"none"})}},mapVideoStatus:e=>({0:"offline",1:"online",2:"failed",3:"compressing"}[e]||"offline"),processQuestions(e){try{e&&e.length>0?this.quizzes=e.map(((e,t)=>{const o=(e.options||[]).map(((e,t)=>({id:String.fromCharCode(65+t),text:e.optionText||e.text||`选项${t+1}`})));let s="A";if(e.options&&e.options.length>0){const t=e.options.find((e=>e.isCorrect));if(t){const o=e.options.indexOf(t);s=String.fromCharCode(65+o)}}return{id:e.id||t+1,question:e.questionText||e.question||`问题${t+1}`,options:o,correctAnswer:s,explanation:e.explanation||""}})):this.quizzes=[]}catch(t){console.error("处理问题数据失败:",t),this.quizzes=[]}},editVideo(){i({url:`/pages/admin/media/upload?id=${this.videoId}`})},goBack(){a()},showDeleteConfirm(){this.showDeletePopup=!0},cancelDelete(){this.showDeletePopup=!1},async handleDelete(){this.showDeletePopup=!1;try{o({title:"删除中..."});const e=await D(this.videoId);s(),e.success?(t({title:"删除成功",icon:"success"}),setTimeout((()=>{l("videoDeleted",{videoId:this.videoId}),a()}),1500)):t({title:e.msg||"删除失败",icon:"none"})}catch(e){console.error("删除视频失败:",e),s(),t({title:"删除失败，请重试",icon:"none"})}},createBatch(){i({url:`/pages/admin/media/publish?id=${this.videoId}`})},isCorrectOption:(e,t)=>"string"==typeof e.correctAnswer?e.correctAnswer===t:!!Array.isArray(e.correctAnswer)&&e.correctAnswer.includes(t),showActionSheet(){this.showActionPopup=!0},closeActionSheet(){this.showActionPopup=!1},handleActionSelect(e){switch(this.showActionPopup=!1,e.name){case"编辑视频":this.editVideo();break;case"创建批次":this.createBatch();break;case"删除视频":this.showDeleteConfirm()}}}},[["render",function(e,t,o,s,i,a){const l=n(c("u-loading-icon"),u),S=q,D=p,T=A,P=n(c("u-tag"),f),j=n(c("u-button"),h),V=n(c("u-card"),m),O=n(c("u-popup"),_),B=n(c("u-action-sheet"),v);return w(),d(D,{class:"page-container"},{default:r((()=>[g(D,{class:"video-preview-section"},{default:r((()=>["compressing"===i.videoInfo.status?(w(),d(D,{key:0,class:"video-processing-overlay"},{default:r((()=>[g(D,{class:"processing-content"},{default:r((()=>[g(l,{mode:"circle",size:"60",color:"#409eff"}),g(S,{class:"processing-text"},{default:r((()=>[x("视频压缩中，暂时无法观看")])),_:1}),g(S,{class:"processing-desc"},{default:r((()=>[x("请稍后再试")])),_:1})])),_:1})])),_:1})):"failed"===i.videoInfo.status?(w(),d(D,{key:1,class:"video-failed-overlay"},{default:r((()=>[g(D,{class:"failed-content"},{default:r((()=>[g(S,{class:"failed-icon"},{default:r((()=>[x("⚠️")])),_:1}),g(S,{class:"failed-text"},{default:r((()=>[x("视频处理失败")])),_:1}),g(S,{class:"failed-desc"},{default:r((()=>[x("请重新上传视频")])),_:1})])),_:1})])),_:1})):(w(),d(T,{key:2,src:i.videoInfo.url,poster:i.videoInfo.cover,class:"video-player",controls:""},null,8,["src","poster"])),g(P,{text:e.getStatusText(i.videoInfo),type:e.getStatusType(i.videoInfo),class:"video-status-tag"},null,8,["text","type"])])),_:1}),g(D,{class:"content-section"},{default:r((()=>[g(V,{title:i.videoInfo.title,padding:10,margin:"20rpx"},{body:r((()=>[g(D,{class:"video-info"},{default:r((()=>[g(D,{class:"info-item"},{default:r((()=>[g(S,{class:"info-label"},{default:r((()=>[x("上传时间:")])),_:1}),g(S,{class:"info-value"},{default:r((()=>[x(y(e.formatDate(i.videoInfo.uploadTime)),1)])),_:1})])),_:1}),g(D,{class:"info-item"},{default:r((()=>[g(S,{class:"info-label"},{default:r((()=>[x("上传者:")])),_:1}),g(S,{class:"info-value"},{default:r((()=>[x(y(i.videoInfo.uploader),1)])),_:1})])),_:1})])),_:1}),i.videoInfo.description?(w(),d(D,{key:0,class:"video-desc"},{default:r((()=>[g(S,null,{default:r((()=>[x(y(i.videoInfo.description),1)])),_:1})])),_:1})):I("",!0),g(D,{class:"operation-section"},{default:r((()=>[g(j,{text:"操作",type:"primary",onClick:a.showActionSheet,"custom-style":{width:"160rpx",height:"60rpx",fontSize:"28rpx"}},null,8,["onClick"])])),_:1})])),_:1},8,["title"])])),_:1}),i.quizzes.length>0?(w(),d(D,{key:0,class:"content-section"},{default:r((()=>[g(V,{padding:10,margin:"20rpx"},{head:r((()=>[g(D,{class:"quiz-card-header"},{default:r((()=>[g(S,{class:"quiz-card-title"},{default:r((()=>[x("相关问题")])),_:1}),g(P,{text:a.rewardText,type:"error",icon:"gift",size:"small"},null,8,["text"])])),_:1})])),body:r((()=>[(w(!0),z(k,null,C(i.quizzes,((e,t)=>(w(),d(D,{class:"quiz-item",key:e.id},{default:r((()=>[g(D,{class:"quiz-header"},{default:r((()=>[g(S,{class:"quiz-number"},{default:r((()=>[x("问题 "+y(t+1),1)])),_:2},1024),g(P,{text:"选择题",type:"primary",size:"mini"})])),_:2},1024),g(D,{class:"quiz-content"},{default:r((()=>[g(S,{class:"quiz-question"},{default:r((()=>[x(y(e.question),1)])),_:2},1024)])),_:2},1024),g(D,{class:"quiz-options"},{default:r((()=>[(w(!0),z(k,null,C(e.options,(t=>(w(),d(D,{class:"option-item",key:t.id},{default:r((()=>[g(S,{class:b(["option-text",{"correct-option":a.isCorrectOption(e,t.id)}])},{default:r((()=>[x(y(t.id)+". "+y(t.text)+" ",1),a.isCorrectOption(e,t.id)?(w(),d(S,{key:0,class:"correct-option-badge"},{default:r((()=>[x("✓")])),_:1})):I("",!0)])),_:2},1032,["class"])])),_:2},1024)))),128))])),_:2},1024),g(D,{class:"quiz-correct-answer"},{default:r((()=>[g(S,{class:"correct-answer-label"},{default:r((()=>[x("正确答案:")])),_:1}),g(S,{class:"correct-answer-value"},{default:r((()=>[x(y(e.correctAnswer),1)])),_:2},1024)])),_:2},1024),e.explanation?(w(),d(D,{key:0,class:"quiz-explanation"},{default:r((()=>[g(S,{class:"explanation-label"},{default:r((()=>[x("解释:")])),_:1}),g(S,{class:"explanation-content"},{default:r((()=>[x(y(e.explanation),1)])),_:2},1024)])),_:2},1024)):I("",!0)])),_:2},1024)))),128))])),_:1})])),_:1})):I("",!0),g(O,{show:i.showDeletePopup,mode:"center",closeOnClickOverlay:!0,onClose:a.cancelDelete},{default:r((()=>[g(D,{class:"delete-popup-container"},{default:r((()=>[g(D,{class:"delete-popup-header"},{default:r((()=>[g(S,{class:"delete-popup-title"},{default:r((()=>[x("确认删除")])),_:1})])),_:1}),g(D,{class:"delete-popup-body"},{default:r((()=>[g(D,{class:"delete-popup-icon"},{default:r((()=>[x("⚠️")])),_:1}),g(S,{class:"delete-popup-message"},{default:r((()=>[x("确定要删除这个视频吗？")])),_:1}),g(S,{class:"delete-popup-desc"},{default:r((()=>[x("相关的问题和奖励也会被删除，此操作不可恢复。")])),_:1})])),_:1}),g(D,{class:"delete-popup-footer"},{default:r((()=>[g(j,{text:"取消",type:"default",onClick:a.cancelDelete,"custom-style":{marginRight:"20rpx"}},null,8,["onClick"]),g(j,{text:"确认删除",type:"error",onClick:a.handleDelete},null,8,["onClick"])])),_:1})])),_:1})])),_:1},8,["show","onClose"]),g(B,{show:i.showActionPopup,actions:i.actionList,onClose:a.closeActionSheet,onSelect:a.handleActionSelect,title:"选择操作",cancelText:"取消"},null,8,["show","actions","onClose","onSelect"])])),_:1})}],["__scopeId","data-v-aea4faea"]]);export{P as default};
