import{_ as t,q as e,H as o,m as i,p as a,N as s,ar as l,A as n,M as d,v as c,g as r,w as h,c as u,j as f,F as m,i as g,o as b,h as v,t as p,K as I,k as _,y as w}from"./index-qwAcQRkd.js";import{g as D}from"./batch.CqXem6BA.js";import{a as y}from"./adminAuthService.2Y4HA-uG.js";import{m as U}from"./media-common.BAxpCZbC.js";import"./format.t5pgP9mx.js";const k=t({mixins:[U],data:()=>({batchId:null,batch:null,batchVideos:[],showActionMenu:!1,showDeleteDialog:!1}),onLoad(t){console.log("batch-detail.vue onLoad 接收到的参数:",t),t.id?(this.batchId=t.id,console.log("设置的 batchId:",this.batchId),this.fetchBatchDetails()):(console.error("未接收到有效的批次ID:",t),e({title:"无效的批次ID",icon:"none",duration:2e3}),setTimeout((()=>o()),1500))},methods:{async fetchBatchDetails(){var t,o,s;try{console.log("fetchBatchDetails 开始加载，batchId:",this.batchId),i({title:"加载中..."});const e=await D(this.batchId);if(console.log("getBatchDetail API 响应:",e),console.log("API 返回的批次数据详情:",e.data),console.log("API 返回的批次ID:",null==(t=e.data)?void 0:t.id),console.log("API 返回的批次名称:",null==(o=e.data)?void 0:o.name),console.log("API 返回的批次标题:",null==(s=e.data)?void 0:s.title),!e.success||!e.data)throw new Error(e.msg||"获取批次详情失败");{const t=e.data;this.batch={id:t.id,batchId:`B${t.id}`,title:t.name||t.title,status:this.mapBatchStatus(t.status),createTime:this.formatDate(t.createTime),startTime:this.formatDate(t.startTime),endTime:this.formatDate(t.endTime),creator:t.creatorName||"未知",videoCount:1,totalViews:t.currentParticipants||0,participants:t.currentParticipants||0,totalReward:t.rewardAmount||0,redPacketAmount:t.redPacketAmount||0,description:t.description||"",videoId:t.videoId,videoTitle:t.videoTitle,videoDescription:t.videoDescription,videoCoverUrl:t.videoCoverUrl,videoUrl:t.videoUrl,videoDuration:t.videoDuration,questions:t.questions||[],statistics:t.statistics||{}},console.log("设置的批次信息:",this.batch),console.log("批次标题:",this.batch.title),console.log("批次描述:",this.batch.description),this.loadBatchVideos()}a()}catch(l){a(),e({title:"加载失败",icon:"none"})}},mapBatchStatus:t=>({0:"pending",1:"active",2:"ended",3:"paused"}[t]||"pending"),loadBatchVideos(){this.batch.videoId?(this.batchVideos=[{id:this.batch.videoId,title:this.batch.videoTitle||this.batch.title||"该批次无视频",description:this.batch.videoDescription||"",videoUrl:this.buildCompleteFileUrl(this.batch.videoUrl),thumbnail:this.buildCompleteFileUrl(this.batch.videoCoverUrl)||"/assets/images/video-cover.jpg",duration:this.batch.videoDuration||0,views:this.batch.totalViews||0,likes:0,comments:0}],console.log("构造的视频数据:",this.batchVideos[0])):(this.batchVideos=[],console.log("批次中没有视频数据"))},goBack(){o()},viewRealTimeData(){s({url:`/pages/admin/media/batch-realtime?id=${this.batchId}`})},showAddVideoModal(){e({title:"添加视频功能开发中",icon:"none"})},viewVideoDetail(t){s({url:`/pages/admin/media/detail?id=${t.id}`})},showVideoMenu(t){l({itemList:["移出批次","查看详情"],success:o=>{0===o.tapIndex?e({title:"移出批次功能开发中",icon:"none"}):1===o.tapIndex&&this.viewVideoDetail(t)}})},getBatchStatusClass:t=>"ended"===t.status?"status-expired":"pending"===t.status?"status-scheduled":"status-active",getBatchStatusText:t=>"ended"===t.status?"已结束":"pending"===t.status?"未开始":"进行中",handleVideoError(t){console.error("视频播放错误:",t.detail);let o="视频加载失败";t.detail&&t.detail.errMsg&&(o+=`: ${t.detail.errMsg}`),this.batchVideos.length>0&&(console.log("视频URL:",this.batchVideos[0].videoUrl),this.batchVideos[0].videoUrl||(this.batchVideos[0].videoUrl="/static/videos/sample.mp4",console.log("使用备用视频URL:",this.batchVideos[0].videoUrl),o="原视频缺失，已替换为示例视频")),e({title:o,icon:"none",duration:3e3})},getVideoShareUrl(){try{let e=null;try{e=y.getLoginInfo(),console.log("adminAuthService.getLoginInfo():",e)}catch(t){console.log("adminAuthService 不可用，从存储获取:",t),e=n("adminLoginInfo"),console.log("uni.getStorageSync(adminLoginInfo):",e)}if(console.log("最终获取的 loginInfo:",e),console.log("loginInfo.userId:",null==e?void 0:e.userId),console.log("loginInfo.username:",null==e?void 0:e.username),console.log("loginInfo.id:",null==e?void 0:e.id),console.log("loginInfo.employeeId:",null==e?void 0:e.employeeId),!e)return console.error("管理员信息缺失:",{loginInfo:e}),"获取链接失败：管理员信息缺失";const o=e.userId||e.username||e.id||e.employeeId||"admin";if(console.log("使用的 sharerId:",o),!this.batch||!this.batch.videoId||!this.batchId)return"获取链接失败：批次信息不完整";const i=function(){if(!window.APP_CONFIG)throw new Error("配置文件未加载！请检查 /static/config/app-config.js 文件是否存在且正确加载。");if(!window.APP_CONFIG.UIProjectUrl)throw new Error("UIProjectUrl 配置项未找到！请检查 app-config.js 文件中的 UIProjectUrl 配置。");return window.APP_CONFIG.UIProjectUrl}();return console.log("当前配置的UIProjectUrl:",i),console.log("window.APP_CONFIG:",window.APP_CONFIG),`${i}/#/pages/video/index?videoId=${this.batch.videoId}&batchId=${this.batchId}&sharerId=${o}`}catch(t){return console.error("获取分享链接失败:",t),this.$toast(t.message||"获取链接失败"),`配置错误：${t.message}`}},copyVideoLink(){try{const t=this.getVideoShareUrl();if(t.includes("获取链接失败"))return void e({title:t,icon:"none",duration:3e3});i({title:"复制中..."}),d({data:t,success:()=>{a(),e({title:"分享链接已复制到剪贴板",icon:"success",duration:2e3}),console.log("分享链接已复制:",t)},fail:t=>{a(),console.error("复制到剪贴板失败:",t),e({title:"复制失败，请手动复制链接",icon:"none",duration:3e3})}})}catch(t){a(),console.error("复制链接失败:",t),e({title:"复制失败，请重试",icon:"none",duration:3e3})}},viewBatchData(){s({url:`/pages/admin/media/batch-data?id=${this.batchId}`})},showActionMenuHandler(){this.showActionMenu=!0},hideActionMenu(){this.showActionMenu=!1},getQuizCount(){return this.batch.questions&&Array.isArray(this.batch.questions)?this.batch.questions.length:0},showDeleteConfirm(){this.hideActionMenu(),this.showDeleteDialog=!0},hideDeleteConfirm(){this.showDeleteDialog=!1},deleteBatch(){this.hideDeleteConfirm(),e({title:"批次已删除",icon:"success"}),setTimeout((()=>{o()}),1500)}}},[["render",function(t,e,o,i,a,s){const l=I,n=_,d=g,D=w;return b(),c(m,null,[r(d,{class:"page-container"},{default:h((()=>[r(d,{class:"main-layout"},{default:h((()=>[r(d,{class:"content-main"},{default:h((()=>[a.batchVideos.length>0?(b(),u(d,{key:0,class:"video-card"},{default:h((()=>[r(d,{class:"video-player-container"},{default:h((()=>[r(l,{src:a.batchVideos[0].videoUrl||"/assets/videos/sample.mp4",poster:a.batchVideos[0].thumbnail,controls:"",class:"video-player",onError:s.handleVideoError},null,8,["src","poster","onError"]),a.batchVideos[0].videoUrl?f("",!0):(b(),u(d,{key:0,class:"video-overlay"},{default:h((()=>[r(n,{class:"overlay-text"},{default:h((()=>[v("视频加载中...")])),_:1})])),_:1}))])),_:1}),r(d,{class:"video-info"},{default:h((()=>[r(d,{class:"video-header"},{default:h((()=>[r(n,{class:"video-title"},{default:h((()=>[v(p(a.batch.title),1)])),_:1})])),_:1}),a.batch.description?(b(),u(d,{key:0,class:"video-description"},{default:h((()=>[r(n,{class:"description-icon"},{default:h((()=>[v("ℹ️")])),_:1}),r(n,{class:"description-text"},{default:h((()=>[v(p(a.batch.description),1)])),_:1})])),_:1})):f("",!0),r(d,{class:"link-info-section"},{default:h((()=>[r(d,{class:"link-info-header"},{default:h((()=>[r(n,{class:"link-icon"},{default:h((()=>[v("�")])),_:1}),r(n,{class:"link-title"},{default:h((()=>[v("分享链接")])),_:1})])),_:1}),r(d,{class:"link-url-display"},{default:h((()=>[r(n,{class:"link-url"},{default:h((()=>[v(p(s.getVideoShareUrl()),1)])),_:1})])),_:1})])),_:1}),r(d,{class:"action-buttons"},{default:h((()=>[r(d,{class:"button-row"},{default:h((()=>[r(D,{class:"action-btn copy-link-btn",onClick:s.copyVideoLink},{default:h((()=>[r(n,{class:"btn-text"},{default:h((()=>[v("复制链接")])),_:1})])),_:1},8,["onClick"]),r(D,{class:"action-btn view-data-btn",onClick:s.viewBatchData},{default:h((()=>[r(n,{class:"btn-text"},{default:h((()=>[v("查看数据")])),_:1})])),_:1},8,["onClick"])])),_:1}),r(d,{class:"button-row full-width"},{default:h((()=>[r(D,{class:"delete-btn danger",onClick:s.showDeleteConfirm},{default:h((()=>[r(n,{class:"btn-text"},{default:h((()=>[v("删除批次")])),_:1})])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})])),_:1})):f("",!0)])),_:1})])),_:1}),0===a.batchVideos.length?(b(),u(d,{key:0,class:"empty-state"},{default:h((()=>[r(d,{class:"empty-icon"},{default:h((()=>[r(n,{class:"iconfont icon-video-off"})])),_:1}),r(n,{class:"empty-title"},{default:h((()=>[v("该批次暂无视频")])),_:1}),r(n,{class:"empty-desc"},{default:h((()=>[v("请添加视频内容或选择其他批次")])),_:1})])),_:1})):f("",!0)])),_:1}),a.showDeleteDialog?(b(),u(d,{key:0,class:"modal-overlay"},{default:h((()=>[r(d,{class:"confirm-modal"},{default:h((()=>[r(d,{class:"modal-header"},{default:h((()=>[r(n,{class:"modal-title"},{default:h((()=>[v("确认删除")])),_:1})])),_:1}),r(d,{class:"modal-body"},{default:h((()=>[r(n,{class:"confirm-text"},{default:h((()=>[v('确定要删除批次"'+p(a.batch.title)+'"吗？删除后无法恢复，请谨慎操作。',1)])),_:1})])),_:1}),r(d,{class:"modal-footer"},{default:h((()=>[r(D,{class:"btn secondary",onClick:s.hideDeleteConfirm},{default:h((()=>[v("取消")])),_:1},8,["onClick"]),r(D,{class:"btn danger",onClick:s.deleteBatch},{default:h((()=>[v("确认删除")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})):f("",!0)],64)}],["__scopeId","data-v-711ebb8b"]]);export{k as default};
