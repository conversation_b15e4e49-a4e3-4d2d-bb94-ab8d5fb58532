import{_ as a,q as t,m as e,p as s,N as l,H as d,c,w as i,i as r,o,g as u,h as n,t as f,n as _,v as h,x as m,F as p,k as b,y as w,a7 as g,L as y}from"./index-qwAcQRkd.js";import{g as v}from"./batch.CqXem6BA.js";import{a as T}from"./dashboard.ZhYzQ-QX.js";import"./adminAuthService.2Y4HA-uG.js";const k=a({data:()=>({batchId:"",batch:{id:"",batchId:"",title:"",status:"",startTime:"",endTime:"",totalViews:0,participants:0,totalReward:0},realTimeData:{currentOnline:0,todayViews:0,todayParticipants:0,todayCompletions:0,completionRate:0},recentViewsData:[],topVideos:[],topUsers:[]}),onLoad(a){a.id&&(this.batchId=a.id,this.loadBatchDetail(),this.loadRealTimeData(),this.loadTopVideos(),this.loadTopUsers())},methods:{async loadBatchDetail(){try{const a=await v(this.batchId);if(!a.success||!a.data)throw new Error(a.msg||"获取批次详情失败");{const t=a.data;this.batch={id:t.id,batchId:`B${t.id}`,title:t.name||t.title,status:this.mapBatchStatus(t.status),createTime:t.createTime,startTime:t.startTime,endTime:t.endTime,creator:t.creatorName||"未知",videoCount:1,totalViews:t.currentParticipants||0,participants:t.currentParticipants||0,totalReward:t.rewardAmount||0,redPacketAmount:t.redPacketAmount||0}}}catch(a){t({title:"加载失败",icon:"none"})}},mapBatchStatus:a=>({0:"pending",1:"active",2:"ended",3:"paused"}[a]||"pending"),async loadRealTimeData(){try{const a={batchId:this.batchId,timeRange:"today"},t=await T(a);if(!t.success||!t.data)throw new Error(t.msg||"获取实时数据失败");{const a=t.data;this.realTimeData={currentOnline:a.currentOnline||0,todayViews:a.todayViews||0,todayParticipants:a.todayParticipants||0,todayCompletions:a.todayCompletions||0,completionRate:a.completionRate||0}}}catch(a){t({title:"加载失败",icon:"none"})}},loadTopVideos(){this.topVideos=[]},loadTopUsers(){this.topUsers=[]},async refreshData(){try{e({title:"刷新数据中..."}),await Promise.all([this.loadRealTimeData(),this.loadTopVideos(),this.loadTopUsers()]),s(),t({title:"数据已更新",icon:"success"})}catch(a){s(),t({title:"刷新失败",icon:"none"})}},getBatchStatusClass:a=>"ended"===a.status?"status-expired":"pending"===a.status?"status-scheduled":"status-active",getBatchStatusText:a=>"ended"===a.status?"已过期":"pending"===a.status?"未开始":"进行中",formatDate(a){if(!a)return"未设置";const t=new Date(a);return`${t.getFullYear()}-${(t.getMonth()+1).toString().padStart(2,"0")}-${t.getDate().toString().padStart(2,"0")}`},viewVideoDetail(a){l({url:`/pages/admin/media/detail?id=${a.id}`})},goBack(){d()}}},[["render",function(a,t,e,s,l,d){const v=b,T=r,k=w,D=y;return o(),c(T,{class:"container"},{default:i((()=>[u(T,{class:"page-header"},{default:i((()=>[u(T,{class:"header-section"},{default:i((()=>[u(T,{class:"title-area"},{default:i((()=>[u(T,{class:"back-btn",onClick:d.goBack},{default:i((()=>[u(v,{class:"iconfont icon-back"})])),_:1},8,["onClick"]),u(T,null,{default:i((()=>[u(v,{class:"page-title"},{default:i((()=>[n(f(l.batch.title),1)])),_:1}),u(T,{class:"page-subtitle"},{default:i((()=>[n("实时数据")])),_:1})])),_:1})])),_:1}),u(T,{class:"action-area"},{default:i((()=>[u(k,{class:"refresh-btn",onClick:d.refreshData},{default:i((()=>[u(v,{class:"refresh-icon"},{default:i((()=>[n("🔄")])),_:1}),u(v,null,{default:i((()=>[n("刷新")])),_:1})])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1}),u(T,{class:"page-header-1"}),u(T,{class:"batch-info-card"},{default:i((()=>[u(T,{class:"batch-title-row"},{default:i((()=>[u(v,{class:"batch-title"},{default:i((()=>[n(f(l.batch.title),1)])),_:1}),u(T,{class:_(["batch-status",d.getBatchStatusClass(l.batch)])},{default:i((()=>[n(f(d.getBatchStatusText(l.batch)),1)])),_:1},8,["class"])])),_:1}),u(T,{class:"batch-id"},{default:i((()=>[n("批次编号: "+f(l.batch.batchId),1)])),_:1}),u(T,{class:"batch-period"},{default:i((()=>[n(f(d.formatDate(l.batch.startTime))+" ~ "+f(d.formatDate(l.batch.endTime)),1)])),_:1})])),_:1}),u(T,{class:"data-overview"},{default:i((()=>[u(T,{class:"data-card"},{default:i((()=>[u(T,{class:"data-value"},{default:i((()=>[n(f(l.realTimeData.currentOnline),1)])),_:1}),u(T,{class:"data-label"},{default:i((()=>[n("当前在线")])),_:1})])),_:1}),u(T,{class:"data-card"},{default:i((()=>[u(T,{class:"data-value"},{default:i((()=>[n(f(l.realTimeData.todayViews),1)])),_:1}),u(T,{class:"data-label"},{default:i((()=>[n("今日观看")])),_:1})])),_:1}),u(T,{class:"data-card"},{default:i((()=>[u(T,{class:"data-value"},{default:i((()=>[n(f(l.realTimeData.todayParticipants),1)])),_:1}),u(T,{class:"data-label"},{default:i((()=>[n("今日参与人")])),_:1})])),_:1}),u(T,{class:"data-card"},{default:i((()=>[u(T,{class:"data-value"},{default:i((()=>[n(f(l.realTimeData.todayCompletions),1)])),_:1}),u(T,{class:"data-label"},{default:i((()=>[n("今日完成")])),_:1})])),_:1})])),_:1}),u(T,{class:"section-card"},{default:i((()=>[u(T,{class:"section-header"},{default:i((()=>[u(v,{class:"section-title"},{default:i((()=>[n("总体数据")])),_:1}),u(v,{class:"section-subtitle"},{default:i((()=>[n("累计统计")])),_:1})])),_:1}),u(T,{class:"total-data"},{default:i((()=>[u(T,{class:"data-row"},{default:i((()=>[u(T,{class:"data-item"},{default:i((()=>[u(v,{class:"data-label"},{default:i((()=>[n("总观看量")])),_:1}),u(v,{class:"data-value primary"},{default:i((()=>[n(f(l.batch.totalViews||0),1)])),_:1})])),_:1}),u(T,{class:"data-item"},{default:i((()=>[u(v,{class:"data-label"},{default:i((()=>[n("总参与人数")])),_:1}),u(v,{class:"data-value success"},{default:i((()=>[n(f(l.batch.participants||0),1)])),_:1})])),_:1})])),_:1}),u(T,{class:"data-row"},{default:i((()=>[u(T,{class:"data-item"},{default:i((()=>[u(v,{class:"data-label"},{default:i((()=>[n("完成率")])),_:1}),u(v,{class:"data-value warning"},{default:i((()=>[n(f(l.realTimeData.completionRate)+"%",1)])),_:1})])),_:1}),u(T,{class:"data-item"},{default:i((()=>[u(v,{class:"data-label"},{default:i((()=>[n("总奖励金额")])),_:1}),u(v,{class:"data-value danger"},{default:i((()=>[n(f(l.batch.totalReward||0)+"元",1)])),_:1})])),_:1})])),_:1})])),_:1}),u(T,{class:"data-charts"},{default:i((()=>[u(T,{class:"chart-title"},{default:i((()=>[n("近7天观看趋势")])),_:1}),u(T,{class:"chart-placeholder"},{default:i((()=>[u(T,{class:"chart-bars"},{default:i((()=>[(o(!0),h(p,null,m(l.recentViewsData,((a,t)=>(o(),c(T,{class:"chart-bar",key:t},{default:i((()=>[u(T,{class:"bar-value",style:g({height:a.height})},null,8,["style"]),u(T,{class:"bar-label"},{default:i((()=>[n(f(a.day),1)])),_:2},1024)])),_:2},1024)))),128))])),_:1})])),_:1})])),_:1})])),_:1}),u(T,{class:"section-card"},{default:i((()=>[u(T,{class:"section-header"},{default:i((()=>[u(v,{class:"section-title"},{default:i((()=>[n("热门视频排行")])),_:1}),u(v,{class:"section-subtitle"},{default:i((()=>[n("今日最受欢迎")])),_:1})])),_:1}),u(T,{class:"video-rank-list"},{default:i((()=>[(o(!0),h(p,null,m(l.topVideos,((a,t)=>(o(),c(T,{class:"video-rank-item",key:a.id,onClick:t=>d.viewVideoDetail(a)},{default:i((()=>[u(T,{class:_(["rank-number",t<3?"top-rank":""])},{default:i((()=>[n(f(t+1),1)])),_:2},1032,["class"]),u(T,{class:"video-thumbnail"},{default:i((()=>[u(D,{src:a.thumbnail,mode:"aspectFill"},null,8,["src"])])),_:2},1024),u(T,{class:"video-info"},{default:i((()=>[u(v,{class:"video-title"},{default:i((()=>[n(f(a.title),1)])),_:2},1024),u(T,{class:"video-stats"},{default:i((()=>[u(T,{class:"stat-item"},{default:i((()=>[u(v,{class:"stat-label"},{default:i((()=>[n("今日观看:")])),_:1}),u(v,{class:"stat-value"},{default:i((()=>[n(f(a.todayViews),1)])),_:2},1024)])),_:2},1024),u(T,{class:"stat-item"},{default:i((()=>[u(v,{class:"stat-label"},{default:i((()=>[n("当前在线:")])),_:1}),u(v,{class:"stat-value"},{default:i((()=>[n(f(a.currentOnline),1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1})])),_:1}),u(T,{class:"section-card"},{default:i((()=>[u(T,{class:"section-header"},{default:i((()=>[u(v,{class:"section-title"},{default:i((()=>[n("活跃用户排行")])),_:1}),u(v,{class:"section-subtitle"},{default:i((()=>[n("最积极用户的活跃")])),_:1})])),_:1}),u(T,{class:"user-rank-list"},{default:i((()=>[(o(!0),h(p,null,m(l.topUsers,((a,t)=>(o(),c(T,{class:"user-rank-item",key:a.id},{default:i((()=>[u(T,{class:_(["rank-number",t<3?"top-rank":""])},{default:i((()=>[n(f(t+1),1)])),_:2},1032,["class"]),u(T,{class:"user-avatar"},{default:i((()=>[u(D,{src:a.avatar,mode:"aspectFill"},null,8,["src"])])),_:2},1024),u(T,{class:"user-info"},{default:i((()=>[u(v,{class:"user-name"},{default:i((()=>[n(f(a.name),1)])),_:2},1024),u(v,{class:"user-type"},{default:i((()=>[n(f(a.type),1)])),_:2},1024)])),_:2},1024),u(T,{class:"user-progress"},{default:i((()=>[u(T,{class:"progress-bar"},{default:i((()=>[u(T,{class:"progress-inner",style:g({width:a.completionRate+"%"})},null,8,["style"])])),_:2},1024),u(v,{class:"progress-text"},{default:i((()=>[n("完成率 "+f(a.completionRate)+"%",1)])),_:2},1024)])),_:2},1024),u(T,{class:"user-reward"},{default:i((()=>[u(v,{class:"reward-value"},{default:i((()=>[n(f(a.totalReward)+"元",1)])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128))])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-e2134ee2"]]);export{k as default};
