import{at as e,q as t,I as r,G as n,l as i,z as o,A as s,B as a,au as c,s as h,O as l,N as u}from"./index-qwAcQRkd.js";function f(e,t=null){if(!window.APP_CONFIG)return t;const r=e.split(".");let n=window.APP_CONFIG;for(const i of r){if(!n||"object"!=typeof n||void 0===n[i])return t;n=n[i]}return n}const p={get BASE_URL(){return f("apiBaseUrl","https://localhost:7048/api")},get TIMEOUT(){return f("apiTimeout",3e4)},get RETRY_COUNT(){return f("apiRetryCount",3)},get RETRY_DELAY(){return f("apiRetryDelay",1e3)},get DEFAULT_HEADERS(){return f("apiDefaultHeaders",{"Content-Type":"application/json",Accept:"application/json"})}};function d(){return p.BASE_URL}function v(r,n="success",i=3e3){try{const t=e();if(t&&t.globalToast)return void t.globalToast.show({message:r,type:n,duration:i})}catch(o){console.warn("全局 toast 组件不可用，使用系统 toast:",o)}t({title:r,icon:{success:"success",error:"none",warning:"none",info:"none"}[n]||"none",duration:i})}function g(e,t=2e3){v(e,"success",t)}function y(e,t=3e3){v(e,"error",t)}function _(e,t="提示"){return new Promise((n=>{r({title:t,content:e,confirmText:"确认",cancelText:"取消",success:e=>{n(e.confirm)},fail:()=>{n(!1)}})}))}async function m(e){!function(e){y(e||"请求失败")}(e||"认证失败"),setTimeout((()=>{mt.logout(),mt.redirectToLogin()}),1e3)}function x(e){return new Promise(((t,r)=>{const o=function(e){e.url.startsWith("http")||(e.url=d()+e.url),e.header={...p.DEFAULT_HEADERS,...e.header};const t=mt.getLoginInfo();return t&&t.accessToken&&(e.header.Authorization=`Bearer ${t.accessToken}`),e.timeout=e.timeout||p.TIMEOUT,e}(e);n({...o,success:e=>{(function(e){const{data:t,statusCode:r}=e;if(200!==r)return 401===r?(m("认证失败"),Promise.reject(new Error("认证失败"))):r>=500?(y("服务器错误"),Promise.reject(new Error("服务器错误"))):(y(`请求失败 (${r})`),Promise.reject(new Error(`请求失败 (${r})`)));if(t&&"object"==typeof t){if("boolean"==typeof t.success){if(t.success)return Promise.resolve(t);{const e=t.msg||"请求失败";return 401===t.code?(m(e),Promise.reject(new Error(e))):Promise.resolve(t)}}const{code:e,msg:r,message:n}=t;if(200===e||0===e)return Promise.resolve(t);if(500===e)return Promise.resolve(t);if(401===e)return m(r||n||"认证失败"),Promise.reject(new Error(r||n||"认证失败"));const i=r||n||"未知错误";return y("接口错误: "+i),Promise.reject(new Error(i))}return Promise.resolve(t)})(e).then(t).catch(r)},fail:e=>{e.errMsg&&e.errMsg.includes("timeout")?y("请求超时，请检查网络连接"):e.errMsg&&e.errMsg.includes("fail")?async function(e){await _("无法连接到服务器，是否重新登录？","连接错误")?(mt.logout(),mt.redirectToLogin(),setTimeout((()=>{i({url:"/pages/login/index"})}),100)):y("已取消重新登录")}():y("请求配置错误: "+(e.errMsg||e.message||"未知错误")),r(e)}})}))}const w={request:x,get:function(e,t={},r={}){return x({url:e,method:"GET",data:t,...r})},post:function(e,t={},r={}){return x({url:e,method:"POST",data:t,...r})},put:function(e,t={},r={}){return x({url:e,method:"PUT",data:t,...r})},delete:function(e,t={},r={}){return x({url:e,method:"DELETE",data:t,...r})},getApiBaseURL:d,showError:y,showConfirm:_};function B(){return w.get("/Auth/userinfo")}const k={login:function(e){return w.post("/Auth/login",e)},logout:function(){return w.post("/Auth/logout")},getUserInfo:B,getCurrentUser:function(){return B()}};var b="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function S(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function A(e){if(e.__esModule)return e;var t=e.default;if("function"==typeof t){var r=function e(){return this instanceof e?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};r.prototype=t.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(e).forEach((function(t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})})),r}var C={exports:{}};var H={exports:{}};const T=A(Object.freeze(Object.defineProperty({__proto__:null,default:{}},Symbol.toStringTag,{value:"Module"})));var z;function E(){return z?H.exports:(z=1,H.exports=(e=e||function(e,t){var r;if("undefined"!=typeof window&&window.crypto&&(r=window.crypto),"undefined"!=typeof self&&self.crypto&&(r=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(r=globalThis.crypto),!r&&"undefined"!=typeof window&&window.msCrypto&&(r=window.msCrypto),!r&&void 0!==b&&b.crypto&&(r=b.crypto),!r)try{r=T}catch(v){}var n=function(){if(r){if("function"==typeof r.getRandomValues)try{return r.getRandomValues(new Uint32Array(1))[0]}catch(v){}if("function"==typeof r.randomBytes)try{return r.randomBytes(4).readInt32LE()}catch(v){}}throw new Error("Native crypto module could not be used to get secure random number.")},i=Object.create||function(){function e(){}return function(t){var r;return e.prototype=t,r=new e,e.prototype=null,r}}(),o={},s=o.lib={},a=s.Base=function(){return{extend:function(e){var t=i(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}}}(),c=s.WordArray=a.extend({init:function(e,r){e=this.words=e||[],this.sigBytes=r!=t?r:4*e.length},toString:function(e){return(e||l).stringify(this)},concat:function(e){var t=this.words,r=e.words,n=this.sigBytes,i=e.sigBytes;if(this.clamp(),n%4)for(var o=0;o<i;o++){var s=r[o>>>2]>>>24-o%4*8&255;t[n+o>>>2]|=s<<24-(n+o)%4*8}else for(var a=0;a<i;a+=4)t[n+a>>>2]=r[a>>>2];return this.sigBytes+=i,this},clamp:function(){var t=this.words,r=this.sigBytes;t[r>>>2]&=4294967295<<32-r%4*8,t.length=e.ceil(r/4)},clone:function(){var e=a.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var t=[],r=0;r<e;r+=4)t.push(n());return new c.init(t,e)}}),h=o.enc={},l=h.Hex={stringify:function(e){for(var t=e.words,r=e.sigBytes,n=[],i=0;i<r;i++){var o=t[i>>>2]>>>24-i%4*8&255;n.push((o>>>4).toString(16)),n.push((15&o).toString(16))}return n.join("")},parse:function(e){for(var t=e.length,r=[],n=0;n<t;n+=2)r[n>>>3]|=parseInt(e.substr(n,2),16)<<24-n%8*4;return new c.init(r,t/2)}},u=h.Latin1={stringify:function(e){for(var t=e.words,r=e.sigBytes,n=[],i=0;i<r;i++){var o=t[i>>>2]>>>24-i%4*8&255;n.push(String.fromCharCode(o))}return n.join("")},parse:function(e){for(var t=e.length,r=[],n=0;n<t;n++)r[n>>>2]|=(255&e.charCodeAt(n))<<24-n%4*8;return new c.init(r,t)}},f=h.Utf8={stringify:function(e){try{return decodeURIComponent(escape(u.stringify(e)))}catch(t){throw new Error("Malformed UTF-8 data")}},parse:function(e){return u.parse(unescape(encodeURIComponent(e)))}},p=s.BufferedBlockAlgorithm=a.extend({reset:function(){this._data=new c.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=f.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var r,n=this._data,i=n.words,o=n.sigBytes,s=this.blockSize,a=o/(4*s),h=(a=t?e.ceil(a):e.max((0|a)-this._minBufferSize,0))*s,l=e.min(4*h,o);if(h){for(var u=0;u<h;u+=s)this._doProcessBlock(i,u);r=i.splice(0,h),n.sigBytes-=l}return new c.init(r,l)},clone:function(){var e=a.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});s.Hasher=p.extend({cfg:a.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){p.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,r){return new e.init(r).finalize(t)}},_createHmacHelper:function(e){return function(t,r){return new d.HMAC.init(e,r).finalize(t)}}});var d=o.algo={};return o}(Math),e));var e}var R,D={exports:{}};function P(){return R?D.exports:(R=1,D.exports=(s=E(),r=(t=s).lib,n=r.Base,i=r.WordArray,(o=t.x64={}).Word=n.extend({init:function(e,t){this.high=e,this.low=t}}),o.WordArray=n.extend({init:function(t,r){t=this.words=t||[],this.sigBytes=r!=e?r:8*t.length},toX32:function(){for(var e=this.words,t=e.length,r=[],n=0;n<t;n++){var o=e[n];r.push(o.high),r.push(o.low)}return i.create(r,this.sigBytes)},clone:function(){for(var e=n.clone.call(this),t=e.words=this.words.slice(0),r=t.length,i=0;i<r;i++)t[i]=t[i].clone();return e}}),s));var e,t,r,n,i,o,s}var I,M={exports:{}};function U(){return I?M.exports:(I=1,M.exports=(e=E(),function(){if("function"==typeof ArrayBuffer){var t=e.lib.WordArray,r=t.init,n=t.init=function(e){if(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),(e instanceof Int8Array||"undefined"!=typeof Uint8ClampedArray&&e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array)&&(e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength)),e instanceof Uint8Array){for(var t=e.byteLength,n=[],i=0;i<t;i++)n[i>>>2]|=e[i]<<24-i%4*8;r.call(this,n,t)}else r.apply(this,arguments)};n.prototype=t}}(),e.lib.WordArray));var e}var L,F={exports:{}};function O(){return L?F.exports:(L=1,F.exports=(e=E(),function(){var t=e,r=t.lib.WordArray,n=t.enc;function i(e){return e<<8&4278255360|e>>>8&16711935}n.Utf16=n.Utf16BE={stringify:function(e){for(var t=e.words,r=e.sigBytes,n=[],i=0;i<r;i+=2){var o=t[i>>>2]>>>16-i%4*8&65535;n.push(String.fromCharCode(o))}return n.join("")},parse:function(e){for(var t=e.length,n=[],i=0;i<t;i++)n[i>>>1]|=e.charCodeAt(i)<<16-i%2*16;return r.create(n,2*t)}},n.Utf16LE={stringify:function(e){for(var t=e.words,r=e.sigBytes,n=[],o=0;o<r;o+=2){var s=i(t[o>>>2]>>>16-o%4*8&65535);n.push(String.fromCharCode(s))}return n.join("")},parse:function(e){for(var t=e.length,n=[],o=0;o<t;o++)n[o>>>1]|=i(e.charCodeAt(o)<<16-o%2*16);return r.create(n,2*t)}}}(),e.enc.Utf16));var e}var N,j={exports:{}};function W(){return N?j.exports:(N=1,j.exports=(e=E(),function(){var t=e,r=t.lib.WordArray;function n(e,t,n){for(var i=[],o=0,s=0;s<t;s++)if(s%4){var a=n[e.charCodeAt(s-1)]<<s%4*2|n[e.charCodeAt(s)]>>>6-s%4*2;i[o>>>2]|=a<<24-o%4*8,o++}return r.create(i,o)}t.enc.Base64={stringify:function(e){var t=e.words,r=e.sigBytes,n=this._map;e.clamp();for(var i=[],o=0;o<r;o+=3)for(var s=(t[o>>>2]>>>24-o%4*8&255)<<16|(t[o+1>>>2]>>>24-(o+1)%4*8&255)<<8|t[o+2>>>2]>>>24-(o+2)%4*8&255,a=0;a<4&&o+.75*a<r;a++)i.push(n.charAt(s>>>6*(3-a)&63));var c=n.charAt(64);if(c)for(;i.length%4;)i.push(c);return i.join("")},parse:function(e){var t=e.length,r=this._map,i=this._reverseMap;if(!i){i=this._reverseMap=[];for(var o=0;o<r.length;o++)i[r.charCodeAt(o)]=o}var s=r.charAt(64);if(s){var a=e.indexOf(s);-1!==a&&(t=a)}return n(e,t,i)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),e.enc.Base64));var e}var K,X={exports:{}};function G(){return K?X.exports:(K=1,X.exports=(e=E(),function(){var t=e,r=t.lib.WordArray;function n(e,t,n){for(var i=[],o=0,s=0;s<t;s++)if(s%4){var a=n[e.charCodeAt(s-1)]<<s%4*2|n[e.charCodeAt(s)]>>>6-s%4*2;i[o>>>2]|=a<<24-o%4*8,o++}return r.create(i,o)}t.enc.Base64url={stringify:function(e,t){void 0===t&&(t=!0);var r=e.words,n=e.sigBytes,i=t?this._safe_map:this._map;e.clamp();for(var o=[],s=0;s<n;s+=3)for(var a=(r[s>>>2]>>>24-s%4*8&255)<<16|(r[s+1>>>2]>>>24-(s+1)%4*8&255)<<8|r[s+2>>>2]>>>24-(s+2)%4*8&255,c=0;c<4&&s+.75*c<n;c++)o.push(i.charAt(a>>>6*(3-c)&63));var h=i.charAt(64);if(h)for(;o.length%4;)o.push(h);return o.join("")},parse:function(e,t){void 0===t&&(t=!0);var r=e.length,i=t?this._safe_map:this._map,o=this._reverseMap;if(!o){o=this._reverseMap=[];for(var s=0;s<i.length;s++)o[i.charCodeAt(s)]=s}var a=i.charAt(64);if(a){var c=e.indexOf(a);-1!==c&&(r=c)}return n(e,r,o)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",_safe_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"}}(),e.enc.Base64url));var e}var q,Z={exports:{}};function V(){return q?Z.exports:(q=1,Z.exports=(e=E(),function(t){var r=e,n=r.lib,i=n.WordArray,o=n.Hasher,s=r.algo,a=[];!function(){for(var e=0;e<64;e++)a[e]=4294967296*t.abs(t.sin(e+1))|0}();var c=s.MD5=o.extend({_doReset:function(){this._hash=new i.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var r=0;r<16;r++){var n=t+r,i=e[n];e[n]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8)}var o=this._hash.words,s=e[t+0],c=e[t+1],p=e[t+2],d=e[t+3],v=e[t+4],g=e[t+5],y=e[t+6],_=e[t+7],m=e[t+8],x=e[t+9],w=e[t+10],B=e[t+11],k=e[t+12],b=e[t+13],S=e[t+14],A=e[t+15],C=o[0],H=o[1],T=o[2],z=o[3];C=h(C,H,T,z,s,7,a[0]),z=h(z,C,H,T,c,12,a[1]),T=h(T,z,C,H,p,17,a[2]),H=h(H,T,z,C,d,22,a[3]),C=h(C,H,T,z,v,7,a[4]),z=h(z,C,H,T,g,12,a[5]),T=h(T,z,C,H,y,17,a[6]),H=h(H,T,z,C,_,22,a[7]),C=h(C,H,T,z,m,7,a[8]),z=h(z,C,H,T,x,12,a[9]),T=h(T,z,C,H,w,17,a[10]),H=h(H,T,z,C,B,22,a[11]),C=h(C,H,T,z,k,7,a[12]),z=h(z,C,H,T,b,12,a[13]),T=h(T,z,C,H,S,17,a[14]),C=l(C,H=h(H,T,z,C,A,22,a[15]),T,z,c,5,a[16]),z=l(z,C,H,T,y,9,a[17]),T=l(T,z,C,H,B,14,a[18]),H=l(H,T,z,C,s,20,a[19]),C=l(C,H,T,z,g,5,a[20]),z=l(z,C,H,T,w,9,a[21]),T=l(T,z,C,H,A,14,a[22]),H=l(H,T,z,C,v,20,a[23]),C=l(C,H,T,z,x,5,a[24]),z=l(z,C,H,T,S,9,a[25]),T=l(T,z,C,H,d,14,a[26]),H=l(H,T,z,C,m,20,a[27]),C=l(C,H,T,z,b,5,a[28]),z=l(z,C,H,T,p,9,a[29]),T=l(T,z,C,H,_,14,a[30]),C=u(C,H=l(H,T,z,C,k,20,a[31]),T,z,g,4,a[32]),z=u(z,C,H,T,m,11,a[33]),T=u(T,z,C,H,B,16,a[34]),H=u(H,T,z,C,S,23,a[35]),C=u(C,H,T,z,c,4,a[36]),z=u(z,C,H,T,v,11,a[37]),T=u(T,z,C,H,_,16,a[38]),H=u(H,T,z,C,w,23,a[39]),C=u(C,H,T,z,b,4,a[40]),z=u(z,C,H,T,s,11,a[41]),T=u(T,z,C,H,d,16,a[42]),H=u(H,T,z,C,y,23,a[43]),C=u(C,H,T,z,x,4,a[44]),z=u(z,C,H,T,k,11,a[45]),T=u(T,z,C,H,A,16,a[46]),C=f(C,H=u(H,T,z,C,p,23,a[47]),T,z,s,6,a[48]),z=f(z,C,H,T,_,10,a[49]),T=f(T,z,C,H,S,15,a[50]),H=f(H,T,z,C,g,21,a[51]),C=f(C,H,T,z,k,6,a[52]),z=f(z,C,H,T,d,10,a[53]),T=f(T,z,C,H,w,15,a[54]),H=f(H,T,z,C,c,21,a[55]),C=f(C,H,T,z,m,6,a[56]),z=f(z,C,H,T,A,10,a[57]),T=f(T,z,C,H,y,15,a[58]),H=f(H,T,z,C,b,21,a[59]),C=f(C,H,T,z,v,6,a[60]),z=f(z,C,H,T,B,10,a[61]),T=f(T,z,C,H,p,15,a[62]),H=f(H,T,z,C,x,21,a[63]),o[0]=o[0]+C|0,o[1]=o[1]+H|0,o[2]=o[2]+T|0,o[3]=o[3]+z|0},_doFinalize:function(){var e=this._data,r=e.words,n=8*this._nDataBytes,i=8*e.sigBytes;r[i>>>5]|=128<<24-i%32;var o=t.floor(n/4294967296),s=n;r[15+(i+64>>>9<<4)]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),r[14+(i+64>>>9<<4)]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),e.sigBytes=4*(r.length+1),this._process();for(var a=this._hash,c=a.words,h=0;h<4;h++){var l=c[h];c[h]=16711935&(l<<8|l>>>24)|4278255360&(l<<24|l>>>8)}return a},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}});function h(e,t,r,n,i,o,s){var a=e+(t&r|~t&n)+i+s;return(a<<o|a>>>32-o)+t}function l(e,t,r,n,i,o,s){var a=e+(t&n|r&~n)+i+s;return(a<<o|a>>>32-o)+t}function u(e,t,r,n,i,o,s){var a=e+(t^r^n)+i+s;return(a<<o|a>>>32-o)+t}function f(e,t,r,n,i,o,s){var a=e+(r^(t|~n))+i+s;return(a<<o|a>>>32-o)+t}r.MD5=o._createHelper(c),r.HmacMD5=o._createHmacHelper(c)}(Math),e.MD5));var e}var Y,$={exports:{}};function J(){return Y?$.exports:(Y=1,$.exports=(a=E(),t=(e=a).lib,r=t.WordArray,n=t.Hasher,i=e.algo,o=[],s=i.SHA1=n.extend({_doReset:function(){this._hash=new r.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var r=this._hash.words,n=r[0],i=r[1],s=r[2],a=r[3],c=r[4],h=0;h<80;h++){if(h<16)o[h]=0|e[t+h];else{var l=o[h-3]^o[h-8]^o[h-14]^o[h-16];o[h]=l<<1|l>>>31}var u=(n<<5|n>>>27)+c+o[h];u+=h<20?1518500249+(i&s|~i&a):h<40?1859775393+(i^s^a):h<60?(i&s|i&a|s&a)-1894007588:(i^s^a)-899497514,c=a,a=s,s=i<<30|i>>>2,i=n,n=u}r[0]=r[0]+n|0,r[1]=r[1]+i|0,r[2]=r[2]+s|0,r[3]=r[3]+a|0,r[4]=r[4]+c|0},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,n=8*e.sigBytes;return t[n>>>5]|=128<<24-n%32,t[14+(n+64>>>9<<4)]=Math.floor(r/4294967296),t[15+(n+64>>>9<<4)]=r,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=n.clone.call(this);return e._hash=this._hash.clone(),e}}),e.SHA1=n._createHelper(s),e.HmacSHA1=n._createHmacHelper(s),a.SHA1));var e,t,r,n,i,o,s,a}var Q,ee={exports:{}};function te(){return Q?ee.exports:(Q=1,ee.exports=(e=E(),function(t){var r=e,n=r.lib,i=n.WordArray,o=n.Hasher,s=r.algo,a=[],c=[];!function(){function e(e){for(var r=t.sqrt(e),n=2;n<=r;n++)if(!(e%n))return!1;return!0}function r(e){return 4294967296*(e-(0|e))|0}for(var n=2,i=0;i<64;)e(n)&&(i<8&&(a[i]=r(t.pow(n,.5))),c[i]=r(t.pow(n,1/3)),i++),n++}();var h=[],l=s.SHA256=o.extend({_doReset:function(){this._hash=new i.init(a.slice(0))},_doProcessBlock:function(e,t){for(var r=this._hash.words,n=r[0],i=r[1],o=r[2],s=r[3],a=r[4],l=r[5],u=r[6],f=r[7],p=0;p<64;p++){if(p<16)h[p]=0|e[t+p];else{var d=h[p-15],v=(d<<25|d>>>7)^(d<<14|d>>>18)^d>>>3,g=h[p-2],y=(g<<15|g>>>17)^(g<<13|g>>>19)^g>>>10;h[p]=v+h[p-7]+y+h[p-16]}var _=n&i^n&o^i&o,m=(n<<30|n>>>2)^(n<<19|n>>>13)^(n<<10|n>>>22),x=f+((a<<26|a>>>6)^(a<<21|a>>>11)^(a<<7|a>>>25))+(a&l^~a&u)+c[p]+h[p];f=u,u=l,l=a,a=s+x|0,s=o,o=i,i=n,n=x+(m+_)|0}r[0]=r[0]+n|0,r[1]=r[1]+i|0,r[2]=r[2]+o|0,r[3]=r[3]+s|0,r[4]=r[4]+a|0,r[5]=r[5]+l|0,r[6]=r[6]+u|0,r[7]=r[7]+f|0},_doFinalize:function(){var e=this._data,r=e.words,n=8*this._nDataBytes,i=8*e.sigBytes;return r[i>>>5]|=128<<24-i%32,r[14+(i+64>>>9<<4)]=t.floor(n/4294967296),r[15+(i+64>>>9<<4)]=n,e.sigBytes=4*r.length,this._process(),this._hash},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}});r.SHA256=o._createHelper(l),r.HmacSHA256=o._createHmacHelper(l)}(Math),e.SHA256));var e}var re,ne={exports:{}};var ie,oe={exports:{}};function se(){return ie?oe.exports:(ie=1,oe.exports=(e=E(),P(),function(){var t=e,r=t.lib.Hasher,n=t.x64,i=n.Word,o=n.WordArray,s=t.algo;function a(){return i.create.apply(i,arguments)}var c=[a(1116352408,3609767458),a(1899447441,602891725),a(3049323471,3964484399),a(3921009573,2173295548),a(961987163,4081628472),a(1508970993,3053834265),a(2453635748,2937671579),a(2870763221,3664609560),a(3624381080,2734883394),a(310598401,1164996542),a(607225278,1323610764),a(1426881987,3590304994),a(1925078388,4068182383),a(2162078206,991336113),a(2614888103,633803317),a(3248222580,3479774868),a(3835390401,2666613458),a(4022224774,944711139),a(264347078,2341262773),a(604807628,2007800933),a(770255983,1495990901),a(1249150122,1856431235),a(1555081692,3175218132),a(1996064986,2198950837),a(2554220882,3999719339),a(2821834349,766784016),a(2952996808,2566594879),a(3210313671,3203337956),a(3336571891,1034457026),a(3584528711,2466948901),a(113926993,3758326383),a(338241895,168717936),a(666307205,1188179964),a(773529912,1546045734),a(1294757372,1522805485),a(1396182291,2643833823),a(1695183700,2343527390),a(1986661051,1014477480),a(2177026350,1206759142),a(2456956037,344077627),a(2730485921,1290863460),a(2820302411,3158454273),a(3259730800,3505952657),a(3345764771,106217008),a(3516065817,3606008344),a(3600352804,1432725776),a(4094571909,1467031594),a(275423344,851169720),a(430227734,3100823752),a(506948616,1363258195),a(659060556,3750685593),a(883997877,3785050280),a(958139571,3318307427),a(1322822218,3812723403),a(1537002063,2003034995),a(1747873779,3602036899),a(1955562222,1575990012),a(2024104815,1125592928),a(2227730452,2716904306),a(2361852424,442776044),a(2428436474,593698344),a(2756734187,3733110249),a(3204031479,2999351573),a(3329325298,3815920427),a(3391569614,3928383900),a(3515267271,566280711),a(3940187606,3454069534),a(4118630271,4000239992),a(116418474,1914138554),a(174292421,2731055270),a(289380356,3203993006),a(460393269,320620315),a(685471733,587496836),a(852142971,1086792851),a(1017036298,365543100),a(1126000580,2618297676),a(1288033470,3409855158),a(1501505948,4234509866),a(1607167915,987167468),a(1816402316,1246189591)],h=[];!function(){for(var e=0;e<80;e++)h[e]=a()}();var l=s.SHA512=r.extend({_doReset:function(){this._hash=new o.init([new i.init(1779033703,4089235720),new i.init(3144134277,2227873595),new i.init(1013904242,4271175723),new i.init(2773480762,1595750129),new i.init(1359893119,2917565137),new i.init(2600822924,725511199),new i.init(528734635,4215389547),new i.init(1541459225,327033209)])},_doProcessBlock:function(e,t){for(var r=this._hash.words,n=r[0],i=r[1],o=r[2],s=r[3],a=r[4],l=r[5],u=r[6],f=r[7],p=n.high,d=n.low,v=i.high,g=i.low,y=o.high,_=o.low,m=s.high,x=s.low,w=a.high,B=a.low,k=l.high,b=l.low,S=u.high,A=u.low,C=f.high,H=f.low,T=p,z=d,E=v,R=g,D=y,P=_,I=m,M=x,U=w,L=B,F=k,O=b,N=S,j=A,W=C,K=H,X=0;X<80;X++){var G,q,Z=h[X];if(X<16)q=Z.high=0|e[t+2*X],G=Z.low=0|e[t+2*X+1];else{var V=h[X-15],Y=V.high,$=V.low,J=(Y>>>1|$<<31)^(Y>>>8|$<<24)^Y>>>7,Q=($>>>1|Y<<31)^($>>>8|Y<<24)^($>>>7|Y<<25),ee=h[X-2],te=ee.high,re=ee.low,ne=(te>>>19|re<<13)^(te<<3|re>>>29)^te>>>6,ie=(re>>>19|te<<13)^(re<<3|te>>>29)^(re>>>6|te<<26),oe=h[X-7],se=oe.high,ae=oe.low,ce=h[X-16],he=ce.high,le=ce.low;q=(q=(q=J+se+((G=Q+ae)>>>0<Q>>>0?1:0))+ne+((G+=ie)>>>0<ie>>>0?1:0))+he+((G+=le)>>>0<le>>>0?1:0),Z.high=q,Z.low=G}var ue,fe=U&F^~U&N,pe=L&O^~L&j,de=T&E^T&D^E&D,ve=z&R^z&P^R&P,ge=(T>>>28|z<<4)^(T<<30|z>>>2)^(T<<25|z>>>7),ye=(z>>>28|T<<4)^(z<<30|T>>>2)^(z<<25|T>>>7),_e=(U>>>14|L<<18)^(U>>>18|L<<14)^(U<<23|L>>>9),me=(L>>>14|U<<18)^(L>>>18|U<<14)^(L<<23|U>>>9),xe=c[X],we=xe.high,Be=xe.low,ke=W+_e+((ue=K+me)>>>0<K>>>0?1:0),be=ye+ve;W=N,K=j,N=F,j=O,F=U,O=L,U=I+(ke=(ke=(ke=ke+fe+((ue+=pe)>>>0<pe>>>0?1:0))+we+((ue+=Be)>>>0<Be>>>0?1:0))+q+((ue+=G)>>>0<G>>>0?1:0))+((L=M+ue|0)>>>0<M>>>0?1:0)|0,I=D,M=P,D=E,P=R,E=T,R=z,T=ke+(ge+de+(be>>>0<ye>>>0?1:0))+((z=ue+be|0)>>>0<ue>>>0?1:0)|0}d=n.low=d+z,n.high=p+T+(d>>>0<z>>>0?1:0),g=i.low=g+R,i.high=v+E+(g>>>0<R>>>0?1:0),_=o.low=_+P,o.high=y+D+(_>>>0<P>>>0?1:0),x=s.low=x+M,s.high=m+I+(x>>>0<M>>>0?1:0),B=a.low=B+L,a.high=w+U+(B>>>0<L>>>0?1:0),b=l.low=b+O,l.high=k+F+(b>>>0<O>>>0?1:0),A=u.low=A+j,u.high=S+N+(A>>>0<j>>>0?1:0),H=f.low=H+K,f.high=C+W+(H>>>0<K>>>0?1:0)},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,n=8*e.sigBytes;return t[n>>>5]|=128<<24-n%32,t[30+(n+128>>>10<<5)]=Math.floor(r/4294967296),t[31+(n+128>>>10<<5)]=r,e.sigBytes=4*t.length,this._process(),this._hash.toX32()},clone:function(){var e=r.clone.call(this);return e._hash=this._hash.clone(),e},blockSize:32});t.SHA512=r._createHelper(l),t.HmacSHA512=r._createHmacHelper(l)}(),e.SHA512));var e}var ae,ce={exports:{}};var he,le={exports:{}};function ue(){return he?le.exports:(he=1,le.exports=(e=E(),P(),function(t){var r=e,n=r.lib,i=n.WordArray,o=n.Hasher,s=r.x64.Word,a=r.algo,c=[],h=[],l=[];!function(){for(var e=1,t=0,r=0;r<24;r++){c[e+5*t]=(r+1)*(r+2)/2%64;var n=(2*e+3*t)%5;e=t%5,t=n}for(e=0;e<5;e++)for(t=0;t<5;t++)h[e+5*t]=t+(2*e+3*t)%5*5;for(var i=1,o=0;o<24;o++){for(var a=0,u=0,f=0;f<7;f++){if(1&i){var p=(1<<f)-1;p<32?u^=1<<p:a^=1<<p-32}128&i?i=i<<1^113:i<<=1}l[o]=s.create(a,u)}}();var u=[];!function(){for(var e=0;e<25;e++)u[e]=s.create()}();var f=a.SHA3=o.extend({cfg:o.cfg.extend({outputLength:512}),_doReset:function(){for(var e=this._state=[],t=0;t<25;t++)e[t]=new s.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(e,t){for(var r=this._state,n=this.blockSize/2,i=0;i<n;i++){var o=e[t+2*i],s=e[t+2*i+1];o=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),s=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),(H=r[i]).high^=s,H.low^=o}for(var a=0;a<24;a++){for(var f=0;f<5;f++){for(var p=0,d=0,v=0;v<5;v++)p^=(H=r[f+5*v]).high,d^=H.low;var g=u[f];g.high=p,g.low=d}for(f=0;f<5;f++){var y=u[(f+4)%5],_=u[(f+1)%5],m=_.high,x=_.low;for(p=y.high^(m<<1|x>>>31),d=y.low^(x<<1|m>>>31),v=0;v<5;v++)(H=r[f+5*v]).high^=p,H.low^=d}for(var w=1;w<25;w++){var B=(H=r[w]).high,k=H.low,b=c[w];b<32?(p=B<<b|k>>>32-b,d=k<<b|B>>>32-b):(p=k<<b-32|B>>>64-b,d=B<<b-32|k>>>64-b);var S=u[h[w]];S.high=p,S.low=d}var A=u[0],C=r[0];for(A.high=C.high,A.low=C.low,f=0;f<5;f++)for(v=0;v<5;v++){var H=r[w=f+5*v],T=u[w],z=u[(f+1)%5+5*v],E=u[(f+2)%5+5*v];H.high=T.high^~z.high&E.high,H.low=T.low^~z.low&E.low}H=r[0];var R=l[a];H.high^=R.high,H.low^=R.low}},_doFinalize:function(){var e=this._data,r=e.words;this._nDataBytes;var n=8*e.sigBytes,o=32*this.blockSize;r[n>>>5]|=1<<24-n%32,r[(t.ceil((n+1)/o)*o>>>5)-1]|=128,e.sigBytes=4*r.length,this._process();for(var s=this._state,a=this.cfg.outputLength/8,c=a/8,h=[],l=0;l<c;l++){var u=s[l],f=u.high,p=u.low;f=16711935&(f<<8|f>>>24)|4278255360&(f<<24|f>>>8),p=16711935&(p<<8|p>>>24)|4278255360&(p<<24|p>>>8),h.push(p),h.push(f)}return new i.init(h,a)},clone:function(){for(var e=o.clone.call(this),t=e._state=this._state.slice(0),r=0;r<25;r++)t[r]=t[r].clone();return e}});r.SHA3=o._createHelper(f),r.HmacSHA3=o._createHmacHelper(f)}(Math),e.SHA3));var e}var fe,pe={exports:{}};var de,ve={exports:{}};function ge(){return de?ve.exports:(de=1,ve.exports=(e=E(),r=(t=e).lib.Base,n=t.enc.Utf8,void(t.algo.HMAC=r.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=n.parse(t));var r=e.blockSize,i=4*r;t.sigBytes>i&&(t=e.finalize(t)),t.clamp();for(var o=this._oKey=t.clone(),s=this._iKey=t.clone(),a=o.words,c=s.words,h=0;h<r;h++)a[h]^=1549556828,c[h]^=909522486;o.sigBytes=s.sigBytes=i,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,r=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(r))}}))));var e,t,r,n}var ye,_e={exports:{}};var me,xe={exports:{}};function we(){return me?xe.exports:(me=1,xe.exports=(a=E(),J(),ge(),t=(e=a).lib,r=t.Base,n=t.WordArray,i=e.algo,o=i.MD5,s=i.EvpKDF=r.extend({cfg:r.extend({keySize:4,hasher:o,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var r,i=this.cfg,o=i.hasher.create(),s=n.create(),a=s.words,c=i.keySize,h=i.iterations;a.length<c;){r&&o.update(r),r=o.update(e).finalize(t),o.reset();for(var l=1;l<h;l++)r=o.finalize(r),o.reset();s.concat(r)}return s.sigBytes=4*c,s}}),e.EvpKDF=function(e,t,r){return s.create(r).compute(e,t)},a.EvpKDF));var e,t,r,n,i,o,s,a}var Be,ke={exports:{}};function be(){return Be?ke.exports:(Be=1,ke.exports=(e=E(),we(),void(e.lib.Cipher||function(t){var r=e,n=r.lib,i=n.Base,o=n.WordArray,s=n.BufferedBlockAlgorithm,a=r.enc;a.Utf8;var c=a.Base64,h=r.algo.EvpKDF,l=n.Cipher=s.extend({cfg:i.extend(),createEncryptor:function(e,t){return this.create(this._ENC_XFORM_MODE,e,t)},createDecryptor:function(e,t){return this.create(this._DEC_XFORM_MODE,e,t)},init:function(e,t,r){this.cfg=this.cfg.extend(r),this._xformMode=e,this._key=t,this.reset()},reset:function(){s.reset.call(this),this._doReset()},process:function(e){return this._append(e),this._process()},finalize:function(e){return e&&this._append(e),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function e(e){return"string"==typeof e?m:y}return function(t){return{encrypt:function(r,n,i){return e(n).encrypt(t,r,n,i)},decrypt:function(r,n,i){return e(n).decrypt(t,r,n,i)}}}}()});n.StreamCipher=l.extend({_doFinalize:function(){return this._process(!0)},blockSize:1});var u=r.mode={},f=n.BlockCipherMode=i.extend({createEncryptor:function(e,t){return this.Encryptor.create(e,t)},createDecryptor:function(e,t){return this.Decryptor.create(e,t)},init:function(e,t){this._cipher=e,this._iv=t}}),p=u.CBC=function(){var e=f.extend();function r(e,r,n){var i,o=this._iv;o?(i=o,this._iv=t):i=this._prevBlock;for(var s=0;s<n;s++)e[r+s]^=i[s]}return e.Encryptor=e.extend({processBlock:function(e,t){var n=this._cipher,i=n.blockSize;r.call(this,e,t,i),n.encryptBlock(e,t),this._prevBlock=e.slice(t,t+i)}}),e.Decryptor=e.extend({processBlock:function(e,t){var n=this._cipher,i=n.blockSize,o=e.slice(t,t+i);n.decryptBlock(e,t),r.call(this,e,t,i),this._prevBlock=o}}),e}(),d=(r.pad={}).Pkcs7={pad:function(e,t){for(var r=4*t,n=r-e.sigBytes%r,i=n<<24|n<<16|n<<8|n,s=[],a=0;a<n;a+=4)s.push(i);var c=o.create(s,n);e.concat(c)},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}};n.BlockCipher=l.extend({cfg:l.cfg.extend({mode:p,padding:d}),reset:function(){var e;l.reset.call(this);var t=this.cfg,r=t.iv,n=t.mode;this._xformMode==this._ENC_XFORM_MODE?e=n.createEncryptor:(e=n.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==e?this._mode.init(this,r&&r.words):(this._mode=e.call(n,this,r&&r.words),this._mode.__creator=e)},_doProcessBlock:function(e,t){this._mode.processBlock(e,t)},_doFinalize:function(){var e,t=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e},blockSize:4});var v=n.CipherParams=i.extend({init:function(e){this.mixIn(e)},toString:function(e){return(e||this.formatter).stringify(this)}}),g=(r.format={}).OpenSSL={stringify:function(e){var t=e.ciphertext,r=e.salt;return(r?o.create([1398893684,1701076831]).concat(r).concat(t):t).toString(c)},parse:function(e){var t,r=c.parse(e),n=r.words;return 1398893684==n[0]&&1701076831==n[1]&&(t=o.create(n.slice(2,4)),n.splice(0,4),r.sigBytes-=16),v.create({ciphertext:r,salt:t})}},y=n.SerializableCipher=i.extend({cfg:i.extend({format:g}),encrypt:function(e,t,r,n){n=this.cfg.extend(n);var i=e.createEncryptor(r,n),o=i.finalize(t),s=i.cfg;return v.create({ciphertext:o,key:r,iv:s.iv,algorithm:e,mode:s.mode,padding:s.padding,blockSize:e.blockSize,formatter:n.format})},decrypt:function(e,t,r,n){return n=this.cfg.extend(n),t=this._parse(t,n.format),e.createDecryptor(r,n).finalize(t.ciphertext)},_parse:function(e,t){return"string"==typeof e?t.parse(e,this):e}}),_=(r.kdf={}).OpenSSL={execute:function(e,t,r,n,i){if(n||(n=o.random(8)),i)s=h.create({keySize:t+r,hasher:i}).compute(e,n);else var s=h.create({keySize:t+r}).compute(e,n);var a=o.create(s.words.slice(t),4*r);return s.sigBytes=4*t,v.create({key:s,iv:a,salt:n})}},m=n.PasswordBasedCipher=y.extend({cfg:y.cfg.extend({kdf:_}),encrypt:function(e,t,r,n){var i=(n=this.cfg.extend(n)).kdf.execute(r,e.keySize,e.ivSize,n.salt,n.hasher);n.iv=i.iv;var o=y.encrypt.call(this,e,t,i.key,n);return o.mixIn(i),o},decrypt:function(e,t,r,n){n=this.cfg.extend(n),t=this._parse(t,n.format);var i=n.kdf.execute(r,e.keySize,e.ivSize,t.salt,n.hasher);return n.iv=i.iv,y.decrypt.call(this,e,t,i.key,n)}})}())));var e}var Se,Ae={exports:{}};var Ce,He={exports:{}};var Te,ze={exports:{}};function Ee(){return Te?ze.exports:(Te=1,ze.exports=(e=E(),be(),
/** @preserve
       * Counter block mode compatible with  Dr Brian Gladman fileenc.c
       * derived from CryptoJS.mode.CTR
       * <NAME_EMAIL>
       */
e.mode.CTRGladman=function(){var t=e.lib.BlockCipherMode.extend();function r(e){if(255==(e>>24&255)){var t=e>>16&255,r=e>>8&255,n=255&e;255===t?(t=0,255===r?(r=0,255===n?n=0:++n):++r):++t,e=0,e+=t<<16,e+=r<<8,e+=n}else e+=1<<24;return e}function n(e){return 0===(e[0]=r(e[0]))&&(e[1]=r(e[1])),e}var i=t.Encryptor=t.extend({processBlock:function(e,t){var r=this._cipher,i=r.blockSize,o=this._iv,s=this._counter;o&&(s=this._counter=o.slice(0),this._iv=void 0),n(s);var a=s.slice(0);r.encryptBlock(a,0);for(var c=0;c<i;c++)e[t+c]^=a[c]}});return t.Decryptor=i,t}(),e.mode.CTRGladman));var e}var Re,De={exports:{}};var Pe,Ie={exports:{}};var Me,Ue={exports:{}};var Le,Fe={exports:{}};var Oe,Ne={exports:{}};var je,We={exports:{}};var Ke,Xe={exports:{}};var Ge,qe={exports:{}};var Ze,Ve={exports:{}};var Ye,$e={exports:{}};function Je(){return Ye?$e.exports:(Ye=1,$e.exports=(e=E(),W(),V(),we(),be(),function(){var t=e,r=t.lib,n=r.WordArray,i=r.BlockCipher,o=t.algo,s=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],a=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],c=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],h=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],l=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],u=o.DES=i.extend({_doReset:function(){for(var e=this._key.words,t=[],r=0;r<56;r++){var n=s[r]-1;t[r]=e[n>>>5]>>>31-n%32&1}for(var i=this._subKeys=[],o=0;o<16;o++){var h=i[o]=[],l=c[o];for(r=0;r<24;r++)h[r/6|0]|=t[(a[r]-1+l)%28]<<31-r%6,h[4+(r/6|0)]|=t[28+(a[r+24]-1+l)%28]<<31-r%6;for(h[0]=h[0]<<1|h[0]>>>31,r=1;r<7;r++)h[r]=h[r]>>>4*(r-1)+3;h[7]=h[7]<<5|h[7]>>>27}var u=this._invSubKeys=[];for(r=0;r<16;r++)u[r]=i[15-r]},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._subKeys)},decryptBlock:function(e,t){this._doCryptBlock(e,t,this._invSubKeys)},_doCryptBlock:function(e,t,r){this._lBlock=e[t],this._rBlock=e[t+1],f.call(this,4,252645135),f.call(this,16,65535),p.call(this,2,858993459),p.call(this,8,16711935),f.call(this,1,1431655765);for(var n=0;n<16;n++){for(var i=r[n],o=this._lBlock,s=this._rBlock,a=0,c=0;c<8;c++)a|=h[c][((s^i[c])&l[c])>>>0];this._lBlock=s,this._rBlock=o^a}var u=this._lBlock;this._lBlock=this._rBlock,this._rBlock=u,f.call(this,1,1431655765),p.call(this,8,16711935),p.call(this,2,858993459),f.call(this,16,65535),f.call(this,4,252645135),e[t]=this._lBlock,e[t+1]=this._rBlock},keySize:2,ivSize:2,blockSize:2});function f(e,t){var r=(this._lBlock>>>e^this._rBlock)&t;this._rBlock^=r,this._lBlock^=r<<e}function p(e,t){var r=(this._rBlock>>>e^this._lBlock)&t;this._lBlock^=r,this._rBlock^=r<<e}t.DES=i._createHelper(u);var d=o.TripleDES=i.extend({_doReset:function(){var e=this._key.words;if(2!==e.length&&4!==e.length&&e.length<6)throw new Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");var t=e.slice(0,2),r=e.length<4?e.slice(0,2):e.slice(2,4),i=e.length<6?e.slice(0,2):e.slice(4,6);this._des1=u.createEncryptor(n.create(t)),this._des2=u.createEncryptor(n.create(r)),this._des3=u.createEncryptor(n.create(i))},encryptBlock:function(e,t){this._des1.encryptBlock(e,t),this._des2.decryptBlock(e,t),this._des3.encryptBlock(e,t)},decryptBlock:function(e,t){this._des3.decryptBlock(e,t),this._des2.encryptBlock(e,t),this._des1.decryptBlock(e,t)},keySize:6,ivSize:2,blockSize:2});t.TripleDES=i._createHelper(d)}(),e.TripleDES));var e}var Qe,et={exports:{}};var tt,rt={exports:{}};var nt,it={exports:{}};var ot,st,at,ct,ht,lt,ut,ft={exports:{}};function pt(){return ot?ft.exports:(ot=1,ft.exports=(e=E(),W(),V(),we(),be(),function(){var t=e,r=t.lib.BlockCipher,n=t.algo;const i=16,o=[608135816,2242054355,320440878,57701188,2752067618,698298832,137296536,3964562569,1160258022,953160567,3193202383,887688300,3232508343,3380367581,1065670069,3041331479,2450970073,2306472731],s=[[3509652390,2564797868,805139163,3491422135,3101798381,1780907670,3128725573,4046225305,614570311,3012652279,134345442,2240740374,1667834072,1901547113,2757295779,4103290238,227898511,1921955416,1904987480,2182433518,2069144605,3260701109,2620446009,720527379,3318853667,677414384,3393288472,3101374703,2390351024,1614419982,1822297739,2954791486,3608508353,3174124327,2024746970,1432378464,3864339955,2857741204,1464375394,1676153920,1439316330,715854006,3033291828,289532110,2706671279,2087905683,3018724369,1668267050,732546397,1947742710,3462151702,2609353502,2950085171,1814351708,2050118529,680887927,999245976,1800124847,3300911131,1713906067,1641548236,4213287313,1216130144,1575780402,4018429277,3917837745,3693486850,3949271944,596196993,3549867205,258830323,2213823033,772490370,2760122372,1774776394,2652871518,566650946,4142492826,1728879713,2882767088,1783734482,3629395816,2517608232,2874225571,1861159788,326777828,3124490320,2130389656,2716951837,967770486,1724537150,2185432712,2364442137,1164943284,2105845187,998989502,3765401048,2244026483,1075463327,1455516326,1322494562,910128902,469688178,1117454909,936433444,3490320968,3675253459,1240580251,122909385,2157517691,634681816,4142456567,3825094682,3061402683,2540495037,79693498,3249098678,1084186820,1583128258,426386531,1761308591,1047286709,322548459,995290223,1845252383,2603652396,3431023940,2942221577,3202600964,3727903485,1712269319,422464435,3234572375,1170764815,3523960633,3117677531,1434042557,442511882,3600875718,1076654713,1738483198,4213154764,2393238008,3677496056,1014306527,4251020053,793779912,2902807211,842905082,4246964064,1395751752,1040244610,2656851899,3396308128,445077038,3742853595,3577915638,679411651,2892444358,2354009459,1767581616,3150600392,3791627101,3102740896,284835224,4246832056,1258075500,768725851,2589189241,3069724005,3532540348,1274779536,3789419226,2764799539,1660621633,3471099624,4011903706,913787905,3497959166,737222580,2514213453,2928710040,3937242737,1804850592,3499020752,2949064160,2386320175,2390070455,2415321851,4061277028,2290661394,2416832540,1336762016,1754252060,3520065937,3014181293,791618072,3188594551,3933548030,2332172193,3852520463,3043980520,413987798,3465142937,3030929376,4245938359,2093235073,3534596313,375366246,2157278981,2479649556,555357303,3870105701,2008414854,3344188149,4221384143,3956125452,2067696032,3594591187,2921233993,2428461,544322398,577241275,1471733935,610547355,4027169054,1432588573,1507829418,2025931657,3646575487,545086370,48609733,2200306550,1653985193,298326376,1316178497,3007786442,2064951626,458293330,2589141269,3591329599,3164325604,727753846,2179363840,146436021,1461446943,4069977195,705550613,3059967265,3887724982,4281599278,3313849956,1404054877,2845806497,146425753,1854211946],[1266315497,3048417604,3681880366,3289982499,290971e4,1235738493,2632868024,2414719590,3970600049,1771706367,1449415276,3266420449,422970021,1963543593,2690192192,3826793022,1062508698,1531092325,1804592342,2583117782,2714934279,4024971509,1294809318,4028980673,1289560198,2221992742,1669523910,35572830,157838143,1052438473,1016535060,1802137761,1753167236,1386275462,3080475397,2857371447,1040679964,2145300060,2390574316,1461121720,2956646967,4031777805,4028374788,33600511,2920084762,1018524850,629373528,3691585981,3515945977,2091462646,2486323059,586499841,988145025,935516892,3367335476,2599673255,2839830854,265290510,3972581182,2759138881,3795373465,1005194799,847297441,406762289,1314163512,1332590856,1866599683,4127851711,750260880,613907577,1450815602,3165620655,3734664991,3650291728,3012275730,3704569646,1427272223,778793252,1343938022,2676280711,2052605720,1946737175,3164576444,3914038668,3967478842,3682934266,1661551462,3294938066,4011595847,840292616,3712170807,616741398,312560963,711312465,1351876610,322626781,1910503582,271666773,2175563734,1594956187,70604529,3617834859,1007753275,1495573769,4069517037,2549218298,2663038764,504708206,2263041392,3941167025,2249088522,1514023603,1998579484,1312622330,694541497,2582060303,2151582166,1382467621,776784248,2618340202,3323268794,2497899128,2784771155,503983604,4076293799,907881277,423175695,432175456,1378068232,4145222326,3954048622,3938656102,3820766613,2793130115,2977904593,26017576,3274890735,3194772133,1700274565,1756076034,4006520079,3677328699,720338349,1533947780,354530856,688349552,3973924725,1637815568,332179504,3949051286,53804574,2852348879,3044236432,1282449977,3583942155,3416972820,4006381244,1617046695,2628476075,3002303598,1686838959,431878346,2686675385,1700445008,1080580658,1009431731,832498133,3223435511,2605976345,2271191193,2516031870,1648197032,4164389018,2548247927,300782431,375919233,238389289,3353747414,2531188641,2019080857,1475708069,455242339,2609103871,448939670,3451063019,1395535956,2413381860,1841049896,1491858159,885456874,4264095073,4001119347,1565136089,3898914787,1108368660,540939232,1173283510,2745871338,3681308437,4207628240,3343053890,4016749493,1699691293,1103962373,3625875870,2256883143,3830138730,1031889488,3479347698,1535977030,4236805024,3251091107,2132092099,1774941330,1199868427,1452454533,157007616,2904115357,342012276,595725824,1480756522,206960106,497939518,591360097,863170706,2375253569,3596610801,1814182875,2094937945,3421402208,1082520231,3463918190,2785509508,435703966,3908032597,1641649973,2842273706,3305899714,1510255612,2148256476,2655287854,3276092548,4258621189,236887753,3681803219,274041037,1734335097,3815195456,3317970021,1899903192,1026095262,4050517792,356393447,2410691914,3873677099,3682840055],[3913112168,2491498743,4132185628,2489919796,1091903735,1979897079,3170134830,3567386728,3557303409,857797738,1136121015,1342202287,507115054,2535736646,337727348,3213592640,1301675037,2528481711,1895095763,1721773893,3216771564,62756741,2142006736,835421444,2531993523,1442658625,3659876326,2882144922,676362277,1392781812,170690266,3921047035,1759253602,3611846912,1745797284,664899054,1329594018,3901205900,3045908486,2062866102,2865634940,3543621612,3464012697,1080764994,553557557,3656615353,3996768171,991055499,499776247,1265440854,648242737,3940784050,980351604,3713745714,1749149687,3396870395,4211799374,3640570775,1161844396,3125318951,1431517754,545492359,4268468663,3499529547,1437099964,2702547544,3433638243,2581715763,2787789398,1060185593,1593081372,2418618748,4260947970,69676912,2159744348,86519011,2512459080,3838209314,1220612927,3339683548,133810670,1090789135,1078426020,1569222167,845107691,3583754449,4072456591,1091646820,628848692,1613405280,3757631651,526609435,236106946,48312990,2942717905,3402727701,1797494240,859738849,992217954,4005476642,2243076622,3870952857,3732016268,765654824,3490871365,2511836413,1685915746,3888969200,1414112111,2273134842,3281911079,4080962846,172450625,2569994100,980381355,4109958455,2819808352,2716589560,2568741196,3681446669,3329971472,1835478071,660984891,3704678404,4045999559,3422617507,3040415634,1762651403,1719377915,3470491036,2693910283,3642056355,3138596744,1364962596,2073328063,1983633131,926494387,3423689081,2150032023,4096667949,1749200295,3328846651,309677260,2016342300,1779581495,3079819751,111262694,1274766160,443224088,298511866,1025883608,3806446537,1145181785,168956806,3641502830,3584813610,1689216846,3666258015,3200248200,1692713982,2646376535,4042768518,1618508792,1610833997,3523052358,4130873264,2001055236,3610705100,2202168115,4028541809,2961195399,1006657119,2006996926,3186142756,1430667929,3210227297,1314452623,4074634658,4101304120,2273951170,1399257539,3367210612,3027628629,1190975929,2062231137,2333990788,2221543033,2438960610,1181637006,548689776,2362791313,3372408396,3104550113,3145860560,296247880,1970579870,3078560182,3769228297,1714227617,3291629107,3898220290,166772364,1251581989,493813264,448347421,195405023,2709975567,677966185,3703036547,1463355134,2715995803,1338867538,1343315457,2802222074,2684532164,233230375,2599980071,2000651841,3277868038,1638401717,4028070440,3237316320,6314154,819756386,300326615,590932579,1405279636,3267499572,3150704214,2428286686,3959192993,3461946742,1862657033,1266418056,963775037,2089974820,2263052895,1917689273,448879540,3550394620,3981727096,150775221,3627908307,1303187396,508620638,2975983352,2726630617,1817252668,1876281319,1457606340,908771278,3720792119,3617206836,2455994898,1729034894,1080033504],[976866871,3556439503,2881648439,1522871579,1555064734,1336096578,3548522304,2579274686,3574697629,3205460757,3593280638,3338716283,3079412587,564236357,2993598910,1781952180,1464380207,3163844217,3332601554,1699332808,1393555694,1183702653,3581086237,1288719814,691649499,2847557200,2895455976,3193889540,2717570544,1781354906,1676643554,2592534050,3230253752,1126444790,2770207658,2633158820,2210423226,2615765581,2414155088,3127139286,673620729,2805611233,1269405062,4015350505,3341807571,4149409754,1057255273,2012875353,2162469141,2276492801,2601117357,993977747,3918593370,2654263191,753973209,36408145,2530585658,25011837,3520020182,2088578344,530523599,2918365339,1524020338,1518925132,3760827505,3759777254,1202760957,3985898139,3906192525,674977740,4174734889,2031300136,2019492241,3983892565,4153806404,3822280332,352677332,2297720250,60907813,90501309,3286998549,1016092578,2535922412,2839152426,457141659,509813237,4120667899,652014361,1966332200,2975202805,55981186,2327461051,676427537,3255491064,2882294119,3433927263,1307055953,942726286,933058658,2468411793,3933900994,4215176142,1361170020,2001714738,2830558078,3274259782,1222529897,1679025792,2729314320,3714953764,1770335741,151462246,3013232138,1682292957,1483529935,471910574,1539241949,458788160,3436315007,1807016891,3718408830,978976581,1043663428,3165965781,1927990952,4200891579,2372276910,3208408903,3533431907,1412390302,2931980059,4132332400,1947078029,3881505623,4168226417,2941484381,1077988104,1320477388,886195818,18198404,3786409e3,2509781533,112762804,3463356488,1866414978,891333506,18488651,661792760,1628790961,3885187036,3141171499,876946877,2693282273,1372485963,791857591,2686433993,3759982718,3167212022,3472953795,2716379847,445679433,3561995674,3504004811,3574258232,54117162,3331405415,2381918588,3769707343,4154350007,1140177722,4074052095,668550556,3214352940,367459370,261225585,2610173221,4209349473,3468074219,3265815641,314222801,3066103646,3808782860,282218597,3406013506,3773591054,379116347,1285071038,846784868,2669647154,3771962079,3550491691,2305946142,453669953,1268987020,3317592352,3279303384,3744833421,2610507566,3859509063,266596637,3847019092,517658769,3462560207,3443424879,370717030,4247526661,2224018117,4143653529,4112773975,2788324899,2477274417,1456262402,2901442914,1517677493,1846949527,2295493580,3734397586,2176403920,1280348187,1908823572,3871786941,846861322,1172426758,3287448474,3383383037,1655181056,3139813346,901632758,1897031941,2986607138,3066810236,3447102507,1393639104,373351379,950779232,625454576,3124240540,4148612726,2007998917,544563296,2244738638,2330496472,2058025392,1291430526,424198748,50039436,29584100,3605783033,2429876329,2791104160,1057563949,3255363231,3075367218,3463963227,1469046755,985887462]];var a={pbox:[],sbox:[]};function c(e,t){let r=t>>24&255,n=t>>16&255,i=t>>8&255,o=255&t,s=e.sbox[0][r]+e.sbox[1][n];return s^=e.sbox[2][i],s+=e.sbox[3][o],s}function h(e,t,r){let n,o=t,s=r;for(let a=0;a<i;++a)o^=e.pbox[a],s=c(e,o)^s,n=o,o=s,s=n;return n=o,o=s,s=n,s^=e.pbox[i],o^=e.pbox[i+1],{left:o,right:s}}function l(e,t,r){let n,o=t,s=r;for(let a=i+1;a>1;--a)o^=e.pbox[a],s=c(e,o)^s,n=o,o=s,s=n;return n=o,o=s,s=n,s^=e.pbox[1],o^=e.pbox[0],{left:o,right:s}}function u(e,t,r){for(let i=0;i<4;i++){e.sbox[i]=[];for(let t=0;t<256;t++)e.sbox[i][t]=s[i][t]}let n=0;for(let s=0;s<i+2;s++)e.pbox[s]=o[s]^t[n],n++,n>=r&&(n=0);let a=0,c=0,l=0;for(let o=0;o<i+2;o+=2)l=h(e,a,c),a=l.left,c=l.right,e.pbox[o]=a,e.pbox[o+1]=c;for(let i=0;i<4;i++)for(let t=0;t<256;t+=2)l=h(e,a,c),a=l.left,c=l.right,e.sbox[i][t]=a,e.sbox[i][t+1]=c;return!0}var f=n.Blowfish=r.extend({_doReset:function(){if(this._keyPriorReset!==this._key){var e=this._keyPriorReset=this._key,t=e.words,r=e.sigBytes/4;u(a,t,r)}},encryptBlock:function(e,t){var r=h(a,e[t],e[t+1]);e[t]=r.left,e[t+1]=r.right},decryptBlock:function(e,t){var r=l(a,e[t],e[t+1]);e[t]=r.left,e[t+1]=r.right},blockSize:2,keySize:4,ivSize:2});t.Blowfish=r._createHelper(f)}(),e.Blowfish));var e}const dt=S(C.exports=function(e){return e}(E(),P(),U(),O(),W(),G(),V(),J(),te(),re||(re=1,ne.exports=(ut=E(),te(),at=(st=ut).lib.WordArray,ct=st.algo,ht=ct.SHA256,lt=ct.SHA224=ht.extend({_doReset:function(){this._hash=new at.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var e=ht._doFinalize.call(this);return e.sigBytes-=4,e}}),st.SHA224=ht._createHelper(lt),st.HmacSHA224=ht._createHmacHelper(lt),ut.SHA224)),se(),function(){return ae?ce.exports:(ae=1,ce.exports=(a=E(),P(),se(),t=(e=a).x64,r=t.Word,n=t.WordArray,i=e.algo,o=i.SHA512,s=i.SHA384=o.extend({_doReset:function(){this._hash=new n.init([new r.init(3418070365,3238371032),new r.init(1654270250,914150663),new r.init(2438529370,812702999),new r.init(355462360,4144912697),new r.init(1731405415,4290775857),new r.init(2394180231,1750603025),new r.init(3675008525,1694076839),new r.init(1203062813,3204075428)])},_doFinalize:function(){var e=o._doFinalize.call(this);return e.sigBytes-=16,e}}),e.SHA384=o._createHelper(s),e.HmacSHA384=o._createHmacHelper(s),a.SHA384));var e,t,r,n,i,o,s,a}(),ue(),function(){return fe?pe.exports:(fe=1,pe.exports=(e=E(),
/** @preserve
      			(c) 2012 by Cédric Mesnil. All rights reserved.
      
      			Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:
      
      			    - Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.
      			    - Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.
      
      			THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
      			*/
function(t){var r=e,n=r.lib,i=n.WordArray,o=n.Hasher,s=r.algo,a=i.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),c=i.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),h=i.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),l=i.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),u=i.create([0,1518500249,1859775393,2400959708,2840853838]),f=i.create([1352829926,1548603684,1836072691,2053994217,0]),p=s.RIPEMD160=o.extend({_doReset:function(){this._hash=i.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var r=0;r<16;r++){var n=t+r,i=e[n];e[n]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8)}var o,s,p,x,w,B,k,b,S,A,C,H=this._hash.words,T=u.words,z=f.words,E=a.words,R=c.words,D=h.words,P=l.words;for(B=o=H[0],k=s=H[1],b=p=H[2],S=x=H[3],A=w=H[4],r=0;r<80;r+=1)C=o+e[t+E[r]]|0,C+=r<16?d(s,p,x)+T[0]:r<32?v(s,p,x)+T[1]:r<48?g(s,p,x)+T[2]:r<64?y(s,p,x)+T[3]:_(s,p,x)+T[4],C=(C=m(C|=0,D[r]))+w|0,o=w,w=x,x=m(p,10),p=s,s=C,C=B+e[t+R[r]]|0,C+=r<16?_(k,b,S)+z[0]:r<32?y(k,b,S)+z[1]:r<48?g(k,b,S)+z[2]:r<64?v(k,b,S)+z[3]:d(k,b,S)+z[4],C=(C=m(C|=0,P[r]))+A|0,B=A,A=S,S=m(b,10),b=k,k=C;C=H[1]+p+S|0,H[1]=H[2]+x+A|0,H[2]=H[3]+w+B|0,H[3]=H[4]+o+k|0,H[4]=H[0]+s+b|0,H[0]=C},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,n=8*e.sigBytes;t[n>>>5]|=128<<24-n%32,t[14+(n+64>>>9<<4)]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),e.sigBytes=4*(t.length+1),this._process();for(var i=this._hash,o=i.words,s=0;s<5;s++){var a=o[s];o[s]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8)}return i},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}});function d(e,t,r){return e^t^r}function v(e,t,r){return e&t|~e&r}function g(e,t,r){return(e|~t)^r}function y(e,t,r){return e&r|t&~r}function _(e,t,r){return e^(t|~r)}function m(e,t){return e<<t|e>>>32-t}r.RIPEMD160=o._createHelper(p),r.HmacRIPEMD160=o._createHmacHelper(p)}(),e.RIPEMD160));var e}(),ge(),function(){return ye?_e.exports:(ye=1,_e.exports=(c=E(),te(),ge(),t=(e=c).lib,r=t.Base,n=t.WordArray,i=e.algo,o=i.SHA256,s=i.HMAC,a=i.PBKDF2=r.extend({cfg:r.extend({keySize:4,hasher:o,iterations:25e4}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var r=this.cfg,i=s.create(r.hasher,e),o=n.create(),a=n.create([1]),c=o.words,h=a.words,l=r.keySize,u=r.iterations;c.length<l;){var f=i.update(t).finalize(a);i.reset();for(var p=f.words,d=p.length,v=f,g=1;g<u;g++){v=i.finalize(v),i.reset();for(var y=v.words,_=0;_<d;_++)p[_]^=y[_]}o.concat(f),h[0]++}return o.sigBytes=4*l,o}}),e.PBKDF2=function(e,t,r){return a.create(r).compute(e,t)},c.PBKDF2));var e,t,r,n,i,o,s,a,c}(),we(),be(),function(){return Se?Ae.exports:(Se=1,Ae.exports=(e=E(),be(),e.mode.CFB=function(){var t=e.lib.BlockCipherMode.extend();function r(e,t,r,n){var i,o=this._iv;o?(i=o.slice(0),this._iv=void 0):i=this._prevBlock,n.encryptBlock(i,0);for(var s=0;s<r;s++)e[t+s]^=i[s]}return t.Encryptor=t.extend({processBlock:function(e,t){var n=this._cipher,i=n.blockSize;r.call(this,e,t,i,n),this._prevBlock=e.slice(t,t+i)}}),t.Decryptor=t.extend({processBlock:function(e,t){var n=this._cipher,i=n.blockSize,o=e.slice(t,t+i);r.call(this,e,t,i,n),this._prevBlock=o}}),t}(),e.mode.CFB));var e}(),function(){return Ce?He.exports:(Ce=1,He.exports=(r=E(),be(),r.mode.CTR=(e=r.lib.BlockCipherMode.extend(),t=e.Encryptor=e.extend({processBlock:function(e,t){var r=this._cipher,n=r.blockSize,i=this._iv,o=this._counter;i&&(o=this._counter=i.slice(0),this._iv=void 0);var s=o.slice(0);r.encryptBlock(s,0),o[n-1]=o[n-1]+1|0;for(var a=0;a<n;a++)e[t+a]^=s[a]}}),e.Decryptor=t,e),r.mode.CTR));var e,t,r}(),Ee(),function(){return Re?De.exports:(Re=1,De.exports=(r=E(),be(),r.mode.OFB=(e=r.lib.BlockCipherMode.extend(),t=e.Encryptor=e.extend({processBlock:function(e,t){var r=this._cipher,n=r.blockSize,i=this._iv,o=this._keystream;i&&(o=this._keystream=i.slice(0),this._iv=void 0),r.encryptBlock(o,0);for(var s=0;s<n;s++)e[t+s]^=o[s]}}),e.Decryptor=t,e),r.mode.OFB));var e,t,r}(),function(){return Pe?Ie.exports:(Pe=1,Ie.exports=(t=E(),be(),t.mode.ECB=((e=t.lib.BlockCipherMode.extend()).Encryptor=e.extend({processBlock:function(e,t){this._cipher.encryptBlock(e,t)}}),e.Decryptor=e.extend({processBlock:function(e,t){this._cipher.decryptBlock(e,t)}}),e),t.mode.ECB));var e,t}(),function(){return Me?Ue.exports:(Me=1,Ue.exports=(e=E(),be(),e.pad.AnsiX923={pad:function(e,t){var r=e.sigBytes,n=4*t,i=n-r%n,o=r+i-1;e.clamp(),e.words[o>>>2]|=i<<24-o%4*8,e.sigBytes+=i},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},e.pad.Ansix923));var e}(),function(){return Le?Fe.exports:(Le=1,Fe.exports=(e=E(),be(),e.pad.Iso10126={pad:function(t,r){var n=4*r,i=n-t.sigBytes%n;t.concat(e.lib.WordArray.random(i-1)).concat(e.lib.WordArray.create([i<<24],1))},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},e.pad.Iso10126));var e}(),function(){return Oe?Ne.exports:(Oe=1,Ne.exports=(e=E(),be(),e.pad.Iso97971={pad:function(t,r){t.concat(e.lib.WordArray.create([2147483648],1)),e.pad.ZeroPadding.pad(t,r)},unpad:function(t){e.pad.ZeroPadding.unpad(t),t.sigBytes--}},e.pad.Iso97971));var e}(),function(){return je?We.exports:(je=1,We.exports=(e=E(),be(),e.pad.ZeroPadding={pad:function(e,t){var r=4*t;e.clamp(),e.sigBytes+=r-(e.sigBytes%r||r)},unpad:function(e){var t=e.words,r=e.sigBytes-1;for(r=e.sigBytes-1;r>=0;r--)if(t[r>>>2]>>>24-r%4*8&255){e.sigBytes=r+1;break}}},e.pad.ZeroPadding));var e}(),function(){return Ke?Xe.exports:(Ke=1,Xe.exports=(e=E(),be(),e.pad.NoPadding={pad:function(){},unpad:function(){}},e.pad.NoPadding));var e}(),function(){return Ge?qe.exports:(Ge=1,qe.exports=(n=E(),be(),t=(e=n).lib.CipherParams,r=e.enc.Hex,e.format.Hex={stringify:function(e){return e.ciphertext.toString(r)},parse:function(e){var n=r.parse(e);return t.create({ciphertext:n})}},n.format.Hex));var e,t,r,n}(),function(){return Ze?Ve.exports:(Ze=1,Ve.exports=(e=E(),W(),V(),we(),be(),function(){var t=e,r=t.lib.BlockCipher,n=t.algo,i=[],o=[],s=[],a=[],c=[],h=[],l=[],u=[],f=[],p=[];!function(){for(var e=[],t=0;t<256;t++)e[t]=t<128?t<<1:t<<1^283;var r=0,n=0;for(t=0;t<256;t++){var d=n^n<<1^n<<2^n<<3^n<<4;d=d>>>8^255&d^99,i[r]=d,o[d]=r;var v=e[r],g=e[v],y=e[g],_=257*e[d]^16843008*d;s[r]=_<<24|_>>>8,a[r]=_<<16|_>>>16,c[r]=_<<8|_>>>24,h[r]=_,_=16843009*y^65537*g^257*v^16843008*r,l[d]=_<<24|_>>>8,u[d]=_<<16|_>>>16,f[d]=_<<8|_>>>24,p[d]=_,r?(r=v^e[e[e[y^v]]],n^=e[e[n]]):r=n=1}}();var d=[0,1,2,4,8,16,32,64,128,27,54],v=n.AES=r.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var e=this._keyPriorReset=this._key,t=e.words,r=e.sigBytes/4,n=4*((this._nRounds=r+6)+1),o=this._keySchedule=[],s=0;s<n;s++)s<r?o[s]=t[s]:(h=o[s-1],s%r?r>6&&s%r==4&&(h=i[h>>>24]<<24|i[h>>>16&255]<<16|i[h>>>8&255]<<8|i[255&h]):(h=i[(h=h<<8|h>>>24)>>>24]<<24|i[h>>>16&255]<<16|i[h>>>8&255]<<8|i[255&h],h^=d[s/r|0]<<24),o[s]=o[s-r]^h);for(var a=this._invKeySchedule=[],c=0;c<n;c++){if(s=n-c,c%4)var h=o[s];else h=o[s-4];a[c]=c<4||s<=4?h:l[i[h>>>24]]^u[i[h>>>16&255]]^f[i[h>>>8&255]]^p[i[255&h]]}}},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._keySchedule,s,a,c,h,i)},decryptBlock:function(e,t){var r=e[t+1];e[t+1]=e[t+3],e[t+3]=r,this._doCryptBlock(e,t,this._invKeySchedule,l,u,f,p,o),r=e[t+1],e[t+1]=e[t+3],e[t+3]=r},_doCryptBlock:function(e,t,r,n,i,o,s,a){for(var c=this._nRounds,h=e[t]^r[0],l=e[t+1]^r[1],u=e[t+2]^r[2],f=e[t+3]^r[3],p=4,d=1;d<c;d++){var v=n[h>>>24]^i[l>>>16&255]^o[u>>>8&255]^s[255&f]^r[p++],g=n[l>>>24]^i[u>>>16&255]^o[f>>>8&255]^s[255&h]^r[p++],y=n[u>>>24]^i[f>>>16&255]^o[h>>>8&255]^s[255&l]^r[p++],_=n[f>>>24]^i[h>>>16&255]^o[l>>>8&255]^s[255&u]^r[p++];h=v,l=g,u=y,f=_}v=(a[h>>>24]<<24|a[l>>>16&255]<<16|a[u>>>8&255]<<8|a[255&f])^r[p++],g=(a[l>>>24]<<24|a[u>>>16&255]<<16|a[f>>>8&255]<<8|a[255&h])^r[p++],y=(a[u>>>24]<<24|a[f>>>16&255]<<16|a[h>>>8&255]<<8|a[255&l])^r[p++],_=(a[f>>>24]<<24|a[h>>>16&255]<<16|a[l>>>8&255]<<8|a[255&u])^r[p++],e[t]=v,e[t+1]=g,e[t+2]=y,e[t+3]=_},keySize:8});t.AES=r._createHelper(v)}(),e.AES));var e}(),Je(),function(){return Qe?et.exports:(Qe=1,et.exports=(e=E(),W(),V(),we(),be(),function(){var t=e,r=t.lib.StreamCipher,n=t.algo,i=n.RC4=r.extend({_doReset:function(){for(var e=this._key,t=e.words,r=e.sigBytes,n=this._S=[],i=0;i<256;i++)n[i]=i;i=0;for(var o=0;i<256;i++){var s=i%r,a=t[s>>>2]>>>24-s%4*8&255;o=(o+n[i]+a)%256;var c=n[i];n[i]=n[o],n[o]=c}this._i=this._j=0},_doProcessBlock:function(e,t){e[t]^=o.call(this)},keySize:8,ivSize:0});function o(){for(var e=this._S,t=this._i,r=this._j,n=0,i=0;i<4;i++){r=(r+e[t=(t+1)%256])%256;var o=e[t];e[t]=e[r],e[r]=o,n|=e[(e[t]+e[r])%256]<<24-8*i}return this._i=t,this._j=r,n}t.RC4=r._createHelper(i);var s=n.RC4Drop=i.extend({cfg:i.cfg.extend({drop:192}),_doReset:function(){i._doReset.call(this);for(var e=this.cfg.drop;e>0;e--)o.call(this)}});t.RC4Drop=r._createHelper(s)}(),e.RC4));var e}(),function(){return tt?rt.exports:(tt=1,rt.exports=(e=E(),W(),V(),we(),be(),function(){var t=e,r=t.lib.StreamCipher,n=t.algo,i=[],o=[],s=[],a=n.Rabbit=r.extend({_doReset:function(){for(var e=this._key.words,t=this.cfg.iv,r=0;r<4;r++)e[r]=16711935&(e[r]<<8|e[r]>>>24)|4278255360&(e[r]<<24|e[r]>>>8);var n=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],i=this._C=[e[2]<<16|e[2]>>>16,4294901760&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,4294901760&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,4294901760&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,4294901760&e[3]|65535&e[0]];for(this._b=0,r=0;r<4;r++)c.call(this);for(r=0;r<8;r++)i[r]^=n[r+4&7];if(t){var o=t.words,s=o[0],a=o[1],h=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),l=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),u=h>>>16|4294901760&l,f=l<<16|65535&h;for(i[0]^=h,i[1]^=u,i[2]^=l,i[3]^=f,i[4]^=h,i[5]^=u,i[6]^=l,i[7]^=f,r=0;r<4;r++)c.call(this)}},_doProcessBlock:function(e,t){var r=this._X;c.call(this),i[0]=r[0]^r[5]>>>16^r[3]<<16,i[1]=r[2]^r[7]>>>16^r[5]<<16,i[2]=r[4]^r[1]>>>16^r[7]<<16,i[3]=r[6]^r[3]>>>16^r[1]<<16;for(var n=0;n<4;n++)i[n]=16711935&(i[n]<<8|i[n]>>>24)|4278255360&(i[n]<<24|i[n]>>>8),e[t+n]^=i[n]},blockSize:4,ivSize:2});function c(){for(var e=this._X,t=this._C,r=0;r<8;r++)o[r]=t[r];for(t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<o[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<o[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<o[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<o[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<o[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<o[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<o[6]>>>0?1:0)|0,this._b=t[7]>>>0<o[7]>>>0?1:0,r=0;r<8;r++){var n=e[r]+t[r],i=65535&n,a=n>>>16,c=((i*i>>>17)+i*a>>>15)+a*a,h=((4294901760&n)*n|0)+((65535&n)*n|0);s[r]=c^h}e[0]=s[0]+(s[7]<<16|s[7]>>>16)+(s[6]<<16|s[6]>>>16)|0,e[1]=s[1]+(s[0]<<8|s[0]>>>24)+s[7]|0,e[2]=s[2]+(s[1]<<16|s[1]>>>16)+(s[0]<<16|s[0]>>>16)|0,e[3]=s[3]+(s[2]<<8|s[2]>>>24)+s[1]|0,e[4]=s[4]+(s[3]<<16|s[3]>>>16)+(s[2]<<16|s[2]>>>16)|0,e[5]=s[5]+(s[4]<<8|s[4]>>>24)+s[3]|0,e[6]=s[6]+(s[5]<<16|s[5]>>>16)+(s[4]<<16|s[4]>>>16)|0,e[7]=s[7]+(s[6]<<8|s[6]>>>24)+s[5]|0}t.Rabbit=r._createHelper(a)}(),e.Rabbit));var e}(),function(){return nt?it.exports:(nt=1,it.exports=(e=E(),W(),V(),we(),be(),function(){var t=e,r=t.lib.StreamCipher,n=t.algo,i=[],o=[],s=[],a=n.RabbitLegacy=r.extend({_doReset:function(){var e=this._key.words,t=this.cfg.iv,r=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],n=this._C=[e[2]<<16|e[2]>>>16,4294901760&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,4294901760&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,4294901760&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,4294901760&e[3]|65535&e[0]];this._b=0;for(var i=0;i<4;i++)c.call(this);for(i=0;i<8;i++)n[i]^=r[i+4&7];if(t){var o=t.words,s=o[0],a=o[1],h=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),l=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),u=h>>>16|4294901760&l,f=l<<16|65535&h;for(n[0]^=h,n[1]^=u,n[2]^=l,n[3]^=f,n[4]^=h,n[5]^=u,n[6]^=l,n[7]^=f,i=0;i<4;i++)c.call(this)}},_doProcessBlock:function(e,t){var r=this._X;c.call(this),i[0]=r[0]^r[5]>>>16^r[3]<<16,i[1]=r[2]^r[7]>>>16^r[5]<<16,i[2]=r[4]^r[1]>>>16^r[7]<<16,i[3]=r[6]^r[3]>>>16^r[1]<<16;for(var n=0;n<4;n++)i[n]=16711935&(i[n]<<8|i[n]>>>24)|4278255360&(i[n]<<24|i[n]>>>8),e[t+n]^=i[n]},blockSize:4,ivSize:2});function c(){for(var e=this._X,t=this._C,r=0;r<8;r++)o[r]=t[r];for(t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<o[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<o[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<o[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<o[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<o[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<o[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<o[6]>>>0?1:0)|0,this._b=t[7]>>>0<o[7]>>>0?1:0,r=0;r<8;r++){var n=e[r]+t[r],i=65535&n,a=n>>>16,c=((i*i>>>17)+i*a>>>15)+a*a,h=((4294901760&n)*n|0)+((65535&n)*n|0);s[r]=c^h}e[0]=s[0]+(s[7]<<16|s[7]>>>16)+(s[6]<<16|s[6]>>>16)|0,e[1]=s[1]+(s[0]<<8|s[0]>>>24)+s[7]|0,e[2]=s[2]+(s[1]<<16|s[1]>>>16)+(s[0]<<16|s[0]>>>16)|0,e[3]=s[3]+(s[2]<<8|s[2]>>>24)+s[1]|0,e[4]=s[4]+(s[3]<<16|s[3]>>>16)+(s[2]<<16|s[2]>>>16)|0,e[5]=s[5]+(s[4]<<8|s[4]>>>24)+s[3]|0,e[6]=s[6]+(s[5]<<16|s[5]>>>16)+(s[4]<<16|s[4]>>>16)|0,e[7]=s[7]+(s[6]<<8|s[6]>>>24)+s[5]|0}t.RabbitLegacy=r._createHelper(a)}(),e.RabbitLegacy));var e}(),pt())),vt={1:"admin",2:"manager",3:"employee",ip_user:"ip_user",user:"user"},gt={1:"超管",2:"管理",3:"员工"},yt="adminLoginInfo",_t="adminUserPermissions";const mt=new class{async authenticate(e,t){try{const r=dt.MD5(t).toString(),n=await k.login({username:e,password:r});if(n&&n.success&&n.data){const t=n.data.userInfo,r=n.data.permissions||[],i=vt[t.UserType]||"employee",o={username:t.Username||e,userId:t.UserId||t.Username||e,nickName:t.NickName,userType:i,userTypeCode:t.UserType,accessToken:n.data.accessToken,permissions:r,loginTime:(new Date).getTime()};return this.saveLoginInfo(o),{success:!0,message:"登录成功",user:o}}return{success:!1,message:n&&n.msg||"登录失败，请检查用户名和密码"}}catch(r){console.error("Admin authentication error:",r);return{success:!1,message:r.message||"登录失败，请重试"}}}saveLoginInfo(e){try{o(yt,e),o(_t,e.permissions)}catch(t){console.error("Error saving login info:",t)}}getLoginInfo(){try{return s(yt)||null}catch(e){return console.error("Error getting login info:",e),null}}isLoggedIn(){const e=this.getLoginInfo();return!(!e||!e.username)}hasPermission(e){try{const t=s(_t)||[];return t.includes("*")||t.includes(e)}catch(t){return console.error("Error checking permission:",t),!1}}getUserType(){const e=this.getLoginInfo();return e?e.userType:null}getUsername(){const e=this.getLoginInfo();return e?e.username:null}getUserName(){const e=this.getLoginInfo();return e?e.name:null}getUserId(){const e=this.getLoginInfo();return e?e.userId:null}getUserDisplayName(){const e=this.getLoginInfo();return e?e.nickName||e.username:null}async logout(){try{try{await k.logout()}catch(e){console.warn("Server logout failed:",e)}return a(yt),a(_t),c(),!0}catch(t){return console.error("Error during logout:",t),!1}}async fetchCurrentUserInfo(){try{const e=await k.getUserInfo();if(e&&e.success&&e.data){const t=e.data,r=this.getLoginInfo();return r&&(r.userId=t.UserId,r.username=t.Username,r.nickName=t.NickName,r.name=t.NickName||t.Username,r.userType=vt[t.UserType]||"employee",r.userTypeCode=t.UserType,r.avatar=t.avatar||"/static/images/avatar-placeholder.png",r.email=t.email||"",r.phone=t.phone||t.mobile||"",r.realName=t.realName||t.nickName||t.username,r.department=t.department||"",r.position=t.position||"",r.lastLoginTime=t.lastLoginTime||"",this.saveLoginInfo(r)),t}return null}catch(e){return console.error("Failed to fetch user info:",e),null}}async getCompleteUserInfo(e=!1){if(!this.isLoggedIn())return{name:"未登录",username:"",userType:"",userTypeText:"游客",avatar:"/static/images/avatar-placeholder.png",email:"",phone:"",realName:"",department:"",position:"",lastLoginTime:""};let t=this.getLoginInfo();if(e||!t.avatar||!t.email){await this.fetchCurrentUserInfo()&&(t=this.getLoginInfo())}return{userId:t.userId||"",name:t.name||t.nickName||t.username||"用户",username:t.username||"",nickName:t.nickName||"",realName:t.realName||t.nickName||t.username||"",userType:t.userType||"",userTypeText:{employee:"员工",manager:"管理",admin:"超管"}[t.userType]||"用户",userTypeCode:t.userTypeCode||"",avatar:t.avatar||"/static/images/avatar-placeholder.png",email:t.email||"",phone:t.phone||"",department:t.department||"",position:t.position||"",lastLoginTime:t.lastLoginTime||"",loginTime:t.loginTime||"",permissions:t.permissions||[]}}isSessionValid(){const e=this.getLoginInfo();if(!e||!e.loginTime)return!1;return(new Date).getTime()-e.loginTime<6048e5}refreshSession(){const e=this.getLoginInfo();e&&(e.loginTime=(new Date).getTime(),this.saveLoginInfo(e))}redirectToLogin(){i({url:"/pages/login/index"})}redirectToMain(){const e=this.getUserType();t({title:"登录成功",icon:"success",duration:1500}),console.log(`登录成功! 用户类型: ${e}`),console.log("正在跳转到仪表板: /pages/index/index"),setTimeout((()=>{i({url:"/pages/index/index",success:()=>{console.log("跳转成功");try{h({success:()=>{console.log("TabBar显示成功")},fail:e=>{console.log("TabBar显示失败:",e)}})}catch(e){console.log("TabBar操作异常:",e)}},fail:e=>{console.error("页面跳转失败:",e),l({url:"/pages/index/index",success:()=>{console.log("switchTab跳转成功")},fail:e=>{console.error("switchTab也失败了:",e),u({url:"/pages/index/index"})}})}})}),1500)}getUserTypeText(){const e=this.getLoginInfo();return e?gt[e.userTypeCode]||"未知":"未登录"}delay(e){return new Promise((t=>setTimeout(t,e)))}},xt=()=>mt.getLoginInfo();export{dt as C,mt as a,y as b,xt as c,d as g,w as r,g as s};
