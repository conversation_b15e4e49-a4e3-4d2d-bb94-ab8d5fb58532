import{I as e,q as t,H as a,_ as r,m as n,p as s,M as i,N as o,O as c,l as u,r as l,a as d,c as g,w as m,P as p,i as f,Q as h,d as y,R as T,T as U,o as _,g as k,h as S,t as I,j as b,v as L,x as v,F as x,k as w}from"./index-qwAcQRkd.js";import{a as C,s as D,b as P}from"./adminAuthService.2Y4HA-uG.js";function $(e={}){return function(e={},r=[],n=[]){if(!C.isLoggedIn())return C.redirectToLogin(),!1;if(!C.isSessionValid())return C.logout(),C.redirectToLogin(),!1;if(n.length>0){const e=C.getUserType();if(!n.includes(e))return t({title:"您没有权限访问此页面",icon:"none"}),setTimeout((()=>{a()}),1500),!1}if(r.length>0&&!r.every((e=>C.hasPermission(e))))return t({title:"您没有权限访问此功能",icon:"none"}),setTimeout((()=>{a()}),1500),!1;return C.refreshSession(),!0}(e,[],[])}function M(){if(!C.isLoggedIn())return{name:"未登录",username:"",userType:"",userTypeText:"游客",avatar:"",email:"",phone:"",department:"",position:""};const e=C.getLoginInfo();return{name:e.nickName||e.username||"未知用户",username:e.username||"",userType:e.userType||"",userTypeText:{employee:"员工",manager:"管理",admin:"超管"}[e.userType]||"未知",avatar:e.avatar||"",email:e.email||"",phone:e.phone||"",department:e.department||"",position:e.position||""}}async function N(e=!1){return await C.getCompleteUserInfo(e)}const z=r({data:()=>({currentUser:{},loading:!1,mainPages:[{name:"数据统计",path:"/pages/index/index",icon:"icon-dashboard"}],personalPages:[{name:"修改密码",path:"/pages/user/change-password",icon:"icon-lock"}],userPages:[{name:"管理信息",path:"/pages/admin/users/manager-list",icon:"icon-users"},{name:"员工信息",path:"/pages/admin/users/employee-list",icon:"icon-staff"},{name:"用户信息",path:"/pages/admin/users/user-list",icon:"icon-user"},{name:"用户审核",path:"/pages/admin/users/audit/user-audit",icon:"icon-audit"}]}),computed:{userTypeText(){return{employee:"员工",manager:"经理",admin:"管理员",agent:"管理"}[this.currentUser.userType]||"用户"},formatLastLoginTime(){if(!this.currentUser.lastLoginTime)return"从未登录";try{const e=new Date(this.currentUser.lastLoginTime),t=new Date;if(t-e<864e5&&e.getDate()===t.getDate())return`今天 ${e.getHours().toString().padStart(2,"0")}:${e.getMinutes().toString().padStart(2,"0")}`;const a=new Date(t);return a.setDate(a.getDate()-1),e.getDate()===a.getDate()&&e.getMonth()===a.getMonth()?`昨天 ${e.getHours().toString().padStart(2,"0")}:${e.getMinutes().toString().padStart(2,"0")}`:`${e.getFullYear()}-${(e.getMonth()+1).toString().padStart(2,"0")}-${e.getDate().toString().padStart(2,"0")} ${e.getHours().toString().padStart(2,"0")}:${e.getMinutes().toString().padStart(2,"0")}`}catch(e){return this.currentUser.lastLoginTime}}},onLoad(e){$(e)&&this.loadUserInfo()},methods:{async loadUserInfo(){try{this.currentUser=M();const e=await N(!1);e&&(this.currentUser=e)}catch(e){console.error("加载用户信息失败:",e),this.currentUser=M()}},async refreshUserInfo(){if(!this.loading){this.loading=!0,n({title:"刷新用户信息..."});try{const e=await N(!0);e&&(this.currentUser=e,t({title:"用户信息已更新",icon:"success"}))}catch(e){console.error("刷新用户信息失败:",e),t({title:"刷新失败，请重试",icon:"none"})}finally{this.loading=!1,s()}}},copyUserId(){this.currentUser.userId?i({data:this.currentUser.userId.toString(),success:()=>{t({title:"用户ID已复制",icon:"success"})},fail:()=>{t({title:"复制失败",icon:"none"})}}):t({title:"用户ID不存在",icon:"none"})},navigateTo(e){o({url:e,fail:()=>{c({url:e,fail:()=>{u({url:e})}})}})},showLogoutConfirm(){!function(t="确定要退出登录吗？"){e({title:"确认退出",content:t,success:async e=>{if(e.confirm)try{await C.logout()?(D("已退出登录"),setTimeout((()=>{C.redirectToLogin()}),1500)):P("退出失败，请重试")}catch(t){P("退出失败，请重试")}}})}()},getIconName:e=>({"icon-dashboard":"grid","icon-video":"play-circle","icon-users":"account","icon-staff":"account-fill","icon-user":"account","icon-info":"info-circle","icon-audit":"checkmark-circle","icon-lock":"lock"}[e]||"more-circle")}},[["render",function(e,t,a,r,n,s){const i=l(d("u-avatar"),p),o=f,c=w,u=l(d("u-tag"),h),C=l(d("u-icon"),y),D=l(d("u-cell"),T),P=l(d("u-cell-group"),U);return _(),g(o,{class:"wx-center-container"},{default:m((()=>[k(o,{class:"user-header"},{default:m((()=>[k(P,{border:!1},{default:m((()=>[k(D,{border:!1,onClick:s.refreshUserInfo},{icon:m((()=>[k(o,{class:"avatar-container"},{default:m((()=>[k(i,{src:n.currentUser.avatar||"/assets/images/avatar-placeholder.png",size:"60",shape:"square"},null,8,["src"])])),_:1})])),title:m((()=>[k(o,{class:"user-info"},{default:m((()=>[k(o,{class:"user-name"},{default:m((()=>[S(I(n.currentUser.realName||n.currentUser.name||"用户"),1)])),_:1}),k(o,{class:"user-meta"},{default:m((()=>[k(c,{class:"user-phone"},{default:m((()=>[S(I(n.currentUser.phone||n.currentUser.username||""),1)])),_:1}),k(u,{text:s.userTypeText,type:"primary",size:"mini",plain:!0},null,8,["text"])])),_:1}),n.currentUser.email||n.currentUser.department?(_(),g(o,{key:0,class:"user-extra-info"},{default:m((()=>[n.currentUser.email?(_(),g(c,{key:0,class:"user-email"},{default:m((()=>[S(I(n.currentUser.email),1)])),_:1})):b("",!0),n.currentUser.department?(_(),g(c,{key:1,class:"user-department"},{default:m((()=>[S(I(n.currentUser.department),1)])),_:1})):b("",!0)])),_:1})):b("",!0)])),_:1})])),"right-icon":m((()=>[k(C,{name:"arrow-right",color:"#c0c4cc",size:"16"})])),_:1},8,["onClick"])])),_:1})])),_:1}),k(o,{class:"menu-section"},{default:m((()=>[k(P,{border:!1},{default:m((()=>[(_(!0),L(x,null,v(n.mainPages,((e,t)=>(_(),g(D,{key:t,title:e.name,isLink:!0,border:t!==n.mainPages.length-1,onClick:t=>s.navigateTo(e.path)},{icon:m((()=>[k(C,{name:s.getIconName(e.icon),size:"20",color:"#666"},null,8,["name"])])),_:2},1032,["title","border","onClick"])))),128))])),_:1})])),_:1}),k(o,{class:"menu-section"},{default:m((()=>[k(P,{border:!1},{default:m((()=>[(_(!0),L(x,null,v(n.personalPages,((e,t)=>(_(),g(D,{key:t,title:e.name,isLink:!0,border:t!==n.personalPages.length-1,onClick:t=>s.navigateTo(e.path)},{icon:m((()=>[k(C,{name:s.getIconName(e.icon),size:"20",color:"#666"},null,8,["name"])])),_:2},1032,["title","border","onClick"])))),128))])),_:1})])),_:1}),k(o,{class:"menu-section"},{default:m((()=>[k(P,{border:!1},{default:m((()=>[(_(!0),L(x,null,v(n.userPages,((e,t)=>(_(),g(D,{key:t,title:e.name,isLink:!0,border:t!==n.userPages.length-1,onClick:t=>s.navigateTo(e.path)},{icon:m((()=>[k(C,{name:s.getIconName(e.icon),size:"20",color:"#666"},null,8,["name"])])),_:2},1032,["title","border","onClick"])))),128))])),_:1})])),_:1}),k(o,{class:"logout-section"},{default:m((()=>[k(P,{border:!1},{default:m((()=>[k(D,{title:"退出登录",isLink:!0,border:!1,onClick:s.showLogoutConfirm},{title:m((()=>[k(c,{class:"logout-text"},{default:m((()=>[S("退出登录")])),_:1})])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-b490b346"]]);export{z as default};
