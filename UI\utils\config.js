/**
 * 应用配置文件
 * 管理API地址、环境配置等全局设置
 * 现在从外部配置文件 (window.APP_CONFIG) 读取配置
 */

// 环境类型
export const ENV_TYPES = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production',
  TEST: 'test'
}

// 当前环境（可以通过构建工具或其他方式动态设置）
export const CURRENT_ENV = ENV_TYPES.DEVELOPMENT

/**
 * 获取外部配置项
 * @param {string} key 配置项键名，支持点号分隔的嵌套键名
 * @param {any} defaultValue 默认值
 * @returns {any} 配置项值
 */
function getExternalConfig(key, defaultValue = null) {
  if (!window.APP_CONFIG) {
    return defaultValue;
  }

  // 支持嵌套键名，如 'storage.expireTime'
  const keys = key.split('.');
  let value = window.APP_CONFIG;

  for (const k of keys) {
    if (value && typeof value === 'object' && value[k] !== undefined) {
      value = value[k];
    } else {
      return defaultValue;
    }
  }

  return value;
}

// API配置 - 从外部配置文件读取
export const API_CONFIG = {
  // 获取API基础URL
  get BASE_URL() {
    return getExternalConfig('apiBaseUrl', 'https://localhost:7048/api');
  },

  // 请求配置
  get TIMEOUT() {
    return getExternalConfig('apiTimeout', 30000);
  },
  get RETRY_COUNT() {
    return getExternalConfig('apiRetryCount', 3);
  },
  get RETRY_DELAY() {
    return getExternalConfig('apiRetryDelay', 1000);
  },

  // 默认请求头
  get DEFAULT_HEADERS() {
    return getExternalConfig('apiDefaultHeaders', {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    });
  }
}

// 存储配置
export const STORAGE_CONFIG = {
  // 存储键名
  KEYS: {
    TOKEN: 'auth_token',
    USER_INFO: 'user_info',
    LOGIN_INFO: 'loginInfo',
    ADMIN_LOGIN_INFO: 'adminLoginInfo',
    USER_PERMISSIONS: 'userPermissions',
    APP_CONFIG: 'app_config'
  },

  // 存储过期时间（毫秒）- 从外部配置文件读取
  get EXPIRE_TIME() {
    const expireTime = getExternalConfig('storage.expireTime', {
      token: 7 * 24 * 60 * 60 * 1000, // 7天
      userInfo: 24 * 60 * 60 * 1000,  // 1天
      cache: 30 * 60 * 1000            // 30分钟
    });

    // 转换为大写键名以保持向后兼容
    return {
      TOKEN: expireTime.token,
      USER_INFO: expireTime.userInfo,
      CACHE: expireTime.cache
    };
  }
}

// 页面路由配置
export const ROUTE_CONFIG = {
  // 登录页面
  LOGIN: '/pages/login/index',

  // 主页面
  HOME: '/pages/index/index',

  // 管理页面
  ADMIN: '/pages/admin/index',

  // 用户页面
  USER: '/pages/user/index',

  // 视频页面
  VIDEO: '/pages/video/index'
}

// 统一权限配置
export const PERMISSION_CONFIG = {
  // 用户角色定义
  USER_ROLES: {
    SUPER_ADMIN: 'super_admin',
    ADMIN: 'admin',
    MANAGER: 'manager',
    AGENT: 'agent', // 管理的别名
    EMPLOYEE: 'employee',
    USER: 'user'
  },

  // 权限级别定义（数字越大权限越高）
  PERMISSION_LEVELS: {
    user: 1,
    employee: 2,
    manager: 3,
    agent: 3,
    admin: 4,
    super_admin: 5
  },

  // 权限类型
  PERMISSIONS: {
    // 基础权限
    VIEW_VIDEOS: 'view_videos',
    TAKE_QUIZ: 'take_quiz',
    VIEW_REWARDS: 'view_rewards',

    // 管理权限
    VIEW_DASHBOARD: 'view_dashboard',
    MANAGE_USERS: 'manage_users',
    MANAGE_VIDEOS: 'manage_videos',
    MANAGE_QUIZ: 'manage_quiz',
    VIEW_REPORTS: 'view_reports',
    MANAGE_EMPLOYEES: 'manage_employees',

    // 高级权限
    SYSTEM_CONFIG: 'system_config',
    USER_MANAGEMENT: 'user_management',
    DATA_EXPORT: 'data_export',

    // 超级管理员权限
    ALL: '*'
  },

  // 角色权限映射
  ROLE_PERMISSIONS: {
    user: ['view_videos', 'take_quiz', 'view_rewards'],
    employee: ['view_videos', 'take_quiz', 'view_rewards', 'view_dashboard'],
    manager: ['view_dashboard', 'manage_users', 'view_reports', 'view_videos', 'take_quiz', 'manage_employees'],
    agent: ['view_dashboard', 'manage_employees', 'view_reports', 'manage_users', 'view_videos', 'take_quiz'],
    admin: ['*'],
    super_admin: ['*']
  },

  // 页面访问权限配置
  PAGE_PERMISSIONS: {
    // 管理后台页面
    '/pages/index/index': ['employee', 'manager', 'agent', 'admin', 'super_admin'],
    '/pages/admin/users/user-management': ['manager', 'agent', 'admin', 'super_admin'],
    '/pages/admin/users/employee-list': ['manager', 'agent', 'admin', 'super_admin'],
    '/pages/admin/users/manager-list': ['admin', 'super_admin'],
    '/pages/admin/media/index': ['employee', 'manager', 'agent', 'admin', 'super_admin'],
    '/pages/admin/media/upload': ['manager', 'agent', 'admin', 'super_admin'],
    '/pages/admin/media/publish': ['manager', 'agent', 'admin', 'super_admin'],

    // 用户页面
    '/pages/video/index': ['user', 'employee', 'manager', 'agent', 'admin', 'super_admin'],
    '/pages/user/index': ['user', 'employee', 'manager', 'agent', 'admin', 'super_admin']
  },

  // 功能权限配置
  FEATURE_PERMISSIONS: {
    // 用户管理功能
    'create_user': ['manager', 'agent', 'admin', 'super_admin'],
    'edit_user': ['manager', 'agent', 'admin', 'super_admin'],
    'delete_user': ['admin', 'super_admin'],
    'disable_user': ['manager', 'agent', 'admin', 'super_admin'],

    // 员工管理功能
    'create_employee': ['manager', 'agent', 'admin', 'super_admin'],
    'edit_employee': ['manager', 'agent', 'admin', 'super_admin'],
    'delete_employee': ['admin', 'super_admin'],

    // 管理员管理功能
    'create_manager': ['admin', 'super_admin'],
    'edit_manager': ['admin', 'super_admin'],
    'delete_manager': ['super_admin'],

    // 视频管理功能
    'upload_video': ['manager', 'agent', 'admin', 'super_admin'],
    'edit_video': ['manager', 'agent', 'admin', 'super_admin'],
    'delete_video': ['admin', 'super_admin'],
    'publish_video': ['manager', 'agent', 'admin', 'super_admin'],

    // 数据导出功能
    'export_data': ['admin', 'super_admin'],
    'view_statistics': ['employee', 'manager', 'agent', 'admin', 'super_admin'],

    // 系统配置功能
    'system_config': ['admin', 'super_admin'],
    'user_audit': ['manager', 'agent', 'admin', 'super_admin']
  }
}

// 应用配置 - 从外部配置文件读取
export const APP_CONFIG = {
  // 应用信息
  get APP_NAME() {
    return getExternalConfig('appName', '视频学习测验系统');
  },
  get VERSION() {
    return getExternalConfig('version', '1.0.0');
  },

  // 功能开关
  get FEATURES() {
    const features = getExternalConfig('features', {
      enableCache: true,          // 是否启用缓存
      enableOffline: false,       // 是否启用离线模式
      enableAnalytics: false      // 是否启用数据分析
    });

    // 转换为大写键名以保持向后兼容
    return {
      ENABLE_CACHE: features.enableCache,
      ENABLE_OFFLINE: features.enableOffline,
      ENABLE_ANALYTICS: features.enableAnalytics
    };
  },

  // UI配置
  get UI() {
    const ui = getExternalConfig('ui', {
      // 主题色彩
      colors: {
        primary: '#186BFF',
        success: '#52C41A',
        warning: '#FAAD14',
        error: '#F5222D',
        info: '#186BFF'
      },

      // 页面配置
      pageSize: 20,               // 默认分页大小
      maxUploadSize: 2 * 1024 * 1024 * 1024, // 最大上传文件大小 2GB

      // 动画配置
      animationDuration: 300      // 默认动画时长
    });

    // 转换为大写键名以保持向后兼容
    return {
      COLORS: {
        PRIMARY: ui.colors.primary,
        SUCCESS: ui.colors.success,
        WARNING: ui.colors.warning,
        ERROR: ui.colors.error,
        INFO: ui.colors.info
      },
      PAGE_SIZE: ui.pageSize,
      MAX_UPLOAD_SIZE: ui.maxUploadSize,
      ANIMATION_DURATION: ui.animationDuration
    };
  }
}

/**
 * 获取当前环境的API基础URL
 * @returns {string} API基础URL
 */
export function getApiBaseURL () {
  return API_CONFIG.BASE_URL;
}

/**
 * 获取完整的API URL
 * @param {string} endpoint API端点
 * @returns {string} 完整的API URL
 */
export function getApiURL (endpoint) {
  const baseURL = getApiBaseURL()
  // 确保endpoint以/开头
  const normalizedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`
  return `${baseURL}${normalizedEndpoint}`
}

/**
 * 检查功能是否启用
 * @param {string} feature 功能名称
 * @returns {boolean} 是否启用
 */
export function isFeatureEnabled (feature) {
  return APP_CONFIG.FEATURES[feature] || false
}

/**
 * 获取存储键名
 * @param {string} key 键名
 * @returns {string} 完整的存储键名
 */
export function getStorageKey (key) {
  return STORAGE_CONFIG.KEYS[key] || key
}

/**
 * 检查是否为开发环境
 * @returns {boolean} 是否为开发环境
 */
export function isDevelopment () {
  return CURRENT_ENV === ENV_TYPES.DEVELOPMENT
}

/**
 * 检查是否为生产环境
 * @returns {boolean} 是否为生产环境
 */
export function isProduction () {
  return CURRENT_ENV === ENV_TYPES.PRODUCTION
}

// 默认导出配置对象
export default {
  ENV_TYPES,
  CURRENT_ENV,
  API_CONFIG,
  STORAGE_CONFIG,
  ROUTE_CONFIG,
  PERMISSION_CONFIG,
  APP_CONFIG,

  // 工具函数
  getApiBaseURL,
  getApiURL,
  isFeatureEnabled,
  getStorageKey,
  isDevelopment,
  isProduction
}
